'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { FaL<PERSON><PERSON><PERSON>, FaTwitter, FaGithub, FaEnvelope, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { getS3ImageUrl } from '@/utils/imageUtils';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageKey?: string;
  imageSrc?: string;
  order: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

const MeetTheExpertsSlider: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Fallback data with bio information
  const fallbackTeamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Jack Sequeira Onyango',
      role: 'Graphics Designer',
      bio: 'With a sharp eye for detail and a deep understanding of design trends and tools like Adobe Creative Suite (Photoshop, Illustrator, InDesign), I help brands tell their stories visually and build lasting connections with their audiences.',
      imageSrc: '/images/team/jack-sequeira.jpg',
      order: 0,
      linkedinUrl: 'https://linkedin.com/in/jacksequeira',
    },
    {
      id: '2',
      name: 'Faith Akinyi',
      role: 'Graphic Designer',
      bio: 'Faith specializes in print design and visual communications. She creates stunning brochures, business cards, and marketing materials that help businesses stand out in competitive markets.',
      imageSrc: '/images/team/faith-akinyi.jpg',
      order: 1,
      linkedinUrl: 'https://linkedin.com/in/faithakinyi',
    },
    {
      id: '3',
      name: 'Anita Kay',
      role: 'Executive Assistant | Social Media Expert',
      bio: 'Anita Kay is a dynamic marketing strategist with a keen eye for consumer behavior and digital trends. She specializes in building brand awareness, driving engagement, and creating compelling marketing campaigns.',
      imageSrc: '/images/team/anita-kay.jpg',
      order: 2,
      linkedinUrl: 'https://linkedin.com/in/anitakay',
    }
  ];

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/team');

        if (!response.ok) {
          throw new Error('Failed to fetch team members');
        }

        const data = await response.json();
        setTeamMembers(data);
      } catch (err) {
        console.error('Error fetching team members:', err);
        setError('Failed to load team members');
        setTeamMembers(fallbackTeamMembers);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  const displayTeamMembers = teamMembers.length > 0 ? teamMembers : fallbackTeamMembers;

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === displayTeamMembers.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? displayTeamMembers.length - 1 : prevIndex - 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (isLoading) {
    return (
      <section className="py-16 md:py-24 relative overflow-hidden bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF5400]"></div>
          </div>
        </div>
      </section>
    );
  }

  const currentMember = displayTeamMembers[currentIndex];
  const getImageSrc = (member: TeamMember) => {
    if (member.imageKey) {
      return getS3ImageUrl(member.imageKey);
    }
    return member.imageSrc || '/images/team/default-avatar.jpg';
  };

  return (
    <section className="py-16 md:py-24 relative overflow-hidden bg-gray-50">
      {/* Modern background elements - matching CompanyStory */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Geometric background pattern */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.02]">
          <div className="absolute top-20 left-10 w-32 h-32 border border-[#0A2647] rotate-45"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-[#FF5400] rotate-12"></div>
          <div className="absolute bottom-32 left-1/4 w-16 h-16 border border-[#0A2647] rotate-45"></div>
        </div>

        {/* Gradient overlays */}
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-bl from-[#FF5400]/5 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-[#0A2647]/5 to-transparent"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced header section - matching CompanyStory */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16 md:mb-20"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white border border-[#0A2647]/10 shadow-sm mb-6">
              <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
              <span className="text-[#0A2647] text-sm font-medium">Our Team</span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#0A2647] mb-6 leading-tight">
              Meet the Experts
            </h2>
            <p className="text-lg md:text-xl text-[#0A2647]/70 max-w-3xl mx-auto leading-relaxed">
              Our talented team of professionals is dedicated to bringing your digital vision to life.
            </p>
          </motion.div>

          {/* Team Members Grid - Desktop: 3 columns, Mobile: Slider */}
          <div className="hidden lg:grid lg:grid-cols-3 gap-8">
            {displayTeamMembers.map((member, index) => (
              <motion.div
                key={member.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="relative">
                  {/* Modern image container */}
                  <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
                    <div className="relative rounded-xl overflow-hidden">
                      <Image
                        src={getImageSrc(member)}
                        alt={member.name}
                        width={400}
                        height={500}
                        className="object-cover w-full aspect-[4/5] transition-transform duration-500 group-hover:scale-105"
                        style={{ objectPosition: "center 10%" }}
                      />

                      {/* Subtle gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-[#0A2647]/80 via-transparent to-transparent"></div>
                    </div>
                  </div>

                  {/* Floating member info card */}
                  <div className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-4 border border-gray-100">
                    <h3 className="text-lg font-bold text-[#0A2647] mb-1">
                      {member.name}
                    </h3>
                    <p className="text-[#0A2647]/70 text-xs font-medium mb-3">
                      {member.role}
                    </p>

                    {/* Social media links */}
                    <div className="flex gap-1">
                      {member.linkedinUrl && (
                        <a
                          href={member.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-6 h-6 rounded-lg bg-[#0A2647]/5 hover:bg-[#0A66C2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="LinkedIn Profile"
                        >
                          <FaLinkedin className="text-xs" />
                        </a>
                      )}
                      {member.twitterUrl && (
                        <a
                          href={member.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-6 h-6 rounded-lg bg-[#0A2647]/5 hover:bg-[#1DA1F2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="Twitter Profile"
                        >
                          <FaTwitter className="text-xs" />
                        </a>
                      )}
                      {member.githubUrl && (
                        <a
                          href={member.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-6 h-6 rounded-lg bg-[#0A2647]/5 hover:bg-gray-800 hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="GitHub Profile"
                        >
                          <FaGithub className="text-xs" />
                        </a>
                      )}
                      {member.emailAddress && (
                        <a
                          href={`mailto:${member.emailAddress}`}
                          className="w-6 h-6 rounded-lg bg-[#0A2647]/5 hover:bg-[#FF5400] hover:text-white flex items-center justify-center text-[0A2647] transition-all duration-300"
                          aria-label="Email"
                        >
                          <FaEnvelope className="text-xs" />
                        </a>
                      )}
                    </div>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute -top-4 -left-4 w-6 h-6 border-2 border-[#FF5400] rounded-full opacity-60"></div>
                  <div className="absolute -bottom-8 -left-8 w-4 h-4 bg-[#FF5400] rounded-full opacity-40"></div>
                </div>

                {/* Bio section below image */}
                <div className="mt-8 bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
                  <p className="text-sm text-[#0A2647]/80 leading-relaxed">
                    {member.bio}
                  </p>

                  {/* Signature line */}
                  <div className="flex items-center gap-4 pt-4 mt-4 border-t border-gray-100">
                    <div className="flex-1 h-px bg-gradient-to-r from-[#FF5400] to-transparent"></div>
                    <div className="text-xs font-medium text-[#0A2647]/60">
                      {member.name}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Mobile Slider View */}
          <div className="lg:hidden">
            <div className="grid grid-cols-1 gap-12 items-center">
              {/* Team member image section */}
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative"
              >
                <div className="relative group">
                  {/* Modern image container - matching CompanyStory */}
                  <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
                    <div className="relative rounded-xl overflow-hidden">
                      <Image
                        src={getImageSrc(currentMember)}
                        alt={currentMember.name}
                        width={600}
                        height={700}
                        className="object-cover w-full aspect-[4/5] transition-transform duration-500 group-hover:scale-105"
                        style={{ objectPosition: "center 10%" }}
                        priority
                      />

                      {/* Subtle gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-[#0A2647]/80 via-transparent to-transparent"></div>
                    </div>
                  </div>

                  {/* Floating member info card */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                    className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
                  >
                    <h3 className="text-xl font-bold text-[#0A2647] mb-1">
                      {currentMember.name}
                    </h3>
                    <p className="text-[#0A2647]/70 text-sm font-medium mb-4">
                      {currentMember.role}
                    </p>

                    {/* Social media links */}
                    <div className="flex gap-2">
                      {currentMember.linkedinUrl && (
                        <a
                          href={currentMember.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#0A66C2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="LinkedIn Profile"
                        >
                          <FaLinkedin className="text-sm" />
                        </a>
                      )}
                      {currentMember.twitterUrl && (
                        <a
                          href={currentMember.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#1DA1F2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="Twitter Profile"
                        >
                          <FaTwitter className="text-sm" />
                        </a>
                      )}
                      {currentMember.githubUrl && (
                        <a
                          href={currentMember.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-gray-800 hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="GitHub Profile"
                        >
                          <FaGithub className="text-sm" />
                        </a>
                      )}
                      {currentMember.emailAddress && (
                        <a
                          href={`mailto:${currentMember.emailAddress}`}
                          className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#FF5400] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300"
                          aria-label="Email"
                        >
                          <FaEnvelope className="text-sm" />
                        </a>
                      )}
                    </div>
                  </motion.div>

                  {/* Decorative elements */}
                  <div className="absolute -top-4 -left-4 w-8 h-8 border-2 border-[#FF5400] rounded-full opacity-60"></div>
                  <div className="absolute -bottom-8 -left-8 w-6 h-6 bg-[#FF5400] rounded-full opacity-40"></div>
                </div>
              </motion.div>

              {/* Content section */}
              <motion.div
                key={`content-${currentIndex}`}
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative"
              >
                {/* Quote icon */}
                <div className="absolute -top-6 -left-2 text-6xl text-[#FF5400]/20 font-serif leading-none">
                  "
                </div>

                <div className="relative bg-white rounded-2xl p-8 md:p-10 shadow-xl border border-gray-100">
                  {/* Quote content */}
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <p className="text-lg md:text-xl text-[#0A2647]/80 leading-relaxed font-medium">
                        {currentMember.bio}
                      </p>
                    </div>

                    {/* Signature line */}
                    <div className="flex items-center gap-4 pt-6 border-t border-gray-100">
                      <div className="flex-1 h-px bg-gradient-to-r from-[#FF5400] to-transparent"></div>
                      <div className="text-sm font-medium text-[#0A2647]/60">
                        {currentMember.name}
                      </div>
                    </div>
                  </div>

                  {/* Decorative corner elements */}
                  <div className="absolute top-4 right-4 w-3 h-3 border-t-2 border-r-2 border-[#FF5400]/30"></div>
                  <div className="absolute bottom-4 left-4 w-3 h-3 border-b-2 border-l-2 border-[#FF5400]/30"></div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Slider controls - Mobile only */}
          <div className="lg:hidden flex items-center justify-center mt-12 gap-8">
            {/* Previous button */}
            <button
              onClick={prevSlide}
              className="w-12 h-12 rounded-full bg-white border border-[#0A2647]/10 hover:border-[#FF5400] hover:bg-[#FF5400] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300 shadow-lg hover:shadow-xl"
              aria-label="Previous team member"
            >
              <FaChevronLeft className="text-sm" />
            </button>

            {/* Dots indicator */}
            <div className="flex gap-3">
              {displayTeamMembers.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-[#FF5400] scale-125'
                      : 'bg-[#0A2647]/20 hover:bg-[#0A2647]/40'
                  }`}
                  aria-label={`Go to team member ${index + 1}`}
                />
              ))}
            </div>

            {/* Next button */}
            <button
              onClick={nextSlide}
              className="w-12 h-12 rounded-full bg-white border border-[#0A2647]/10 hover:border-[#FF5400] hover:bg-[#FF5400] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300 shadow-lg hover:shadow-xl"
              aria-label="Next team member"
            >
              <FaChevronRight className="text-sm" />
            </button>
          </div>

          {/* Team member counter - Mobile only */}
          <div className="lg:hidden text-center mt-8">
            <span className="text-sm text-[#0A2647]/60 font-medium">
              {currentIndex + 1} of {displayTeamMembers.length}
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MeetTheExpertsSlider;
