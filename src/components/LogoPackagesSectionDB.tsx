'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import SmartPrice from './SmartPrice';
import CurrencySelector from './CurrencySelector';

interface LogoPackage {
  id: string;
  name: string;
  price: number;
  description: string | null;
  features: string[];
  isActive: boolean;
  isPopular: boolean;
  sortOrder: number;
  whatsappMessage: string | null;
}

export default function LogoPackagesSectionDB() {
  const [packages, setPackages] = useState<LogoPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    fetchPackages();
  }, []);

  const fetchPackages = async () => {
    try {
      const response = await fetch('/api/logo-packages');
      const result = await response.json();
      
      if (result.success) {
        setPackages(result.data);
      } else {
        console.error('Failed to fetch packages:', result.error);
        // Fallback to hardcoded data if API fails
        setPackages(getHardcodedPackages());
      }
    } catch (error) {
      console.error('Error fetching packages:', error);
      // Fallback to hardcoded data if API fails
      setPackages(getHardcodedPackages());
    } finally {
      setLoading(false);
    }
  };

  // Fallback hardcoded packages
  const getHardcodedPackages = (): LogoPackage[] => [
    {
      id: 'mkulima-mdogo',
      name: 'Mkulima Mdogo Package',
      price: 1500,
      description: 'Affordable logo design for small-scale Kenyan businesses',
      features: [
        '2 Unique Concepts',
        'Final files in PNG, JPG, and PDF formats',
        '2 Rounds of Revision',
        'Quick 24-hour delivery',
        'WhatsApp support'
      ],
      isActive: true,
      isPopular: false,
      sortOrder: 0,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Mkulima Mdogo Package (KSH 1,500) that includes:\n• 2 Unique Concepts\n• Final files in PNG, JPG, and PDF formats\n• 2 Rounds of Revision\n• Quick 24-hour delivery\n• WhatsApp support\n\nCan you help me with my logo design?"
    },
    {
      id: 'basic',
      name: 'Basic Package',
      price: 5000,
      description: 'Perfect for small businesses and startups',
      features: [
        '3 Unique Concepts',
        'Professional files (AI, EPS, SVG)',
        '4 Rounds Of Revision',
        'Business Card Design',
        'Letterhead Design'
      ],
      isActive: true,
      isPopular: false,
      sortOrder: 1,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Basic Package (KSH 5,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 4 Rounds Of Revision\n• Business Card Design\n• Letterhead Design\n\nCan you help me with my logo design?"
    },
    {
      id: 'standard',
      name: 'Standard Package',
      price: 15000,
      description: 'Logo design with comprehensive brand guidelines and stationery',
      features: [
        '3 Unique Concepts',
        'Professional files (AI, EPS, SVG)',
        '5 Rounds Of Revision',
        'Basic Brand Guidelines',
        'Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)'
      ],
      isActive: true,
      isPopular: true,
      sortOrder: 2,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Standard Package (KSH 15,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 5 Rounds Of Revision\n• Basic Brand Guidelines\n• Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)\n\nCan you help me with my professional logo design?"
    },
    {
      id: 'premium',
      name: 'Premium Package',
      price: 35000,
      description: 'Complete logo design with comprehensive brand guidelines and premium stationery',
      features: [
        '3 Unique Concepts',
        'Professional files (AI, EPS, SVG)',
        '6 Rounds Of Revision',
        'Comprehensive Brand Guidelines',
        'Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)',
        'Social Media Kit',
        'Brand Color Palette',
        'Logo Usage Guidelines'
      ],
      isActive: true,
      isPopular: false,
      sortOrder: 3,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Premium Package (KSH 35,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 6 Rounds Of Revision\n• Comprehensive Brand Guidelines\n• Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)\n• Social Media Kit\n• Brand Color Palette\n• Logo Usage Guidelines\n\nCan you help me with my premium logo design?"
    }
  ];

  const handleOrderClick = (pkg: LogoPackage) => {
    // Navigate to the dedicated order page
    window.location.href = `/logo-order/${pkg.id}`;
  };



  if (!mounted) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Logo Design Packages
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Choose the perfect logo design package for your business needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((index) => (
              <div
                key={index}
                className="relative bg-gray-100 rounded-xl overflow-hidden shadow-md h-[500px] animate-pulse"
              ></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (loading) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Logo Design Packages
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Loading packages...
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((index) => (
              <div
                key={index}
                className="relative bg-gray-100 rounded-xl overflow-hidden shadow-md h-[500px] animate-pulse"
              ></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-[#FF5400] opacity-5 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#FF5400] opacity-5 rounded-full translate-x-1/3 translate-y-1/3"></div>
      <div className="absolute top-1/4 right-10 w-20 h-20 bg-[#FF5400] opacity-5 rounded-full"></div>
      <div className="absolute bottom-1/4 left-10 w-32 h-32 bg-[#FF5400] opacity-5 rounded-full"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#0A2647]">
            Logo Design Packages
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto text-base mb-6">
            Choose the perfect logo design package for your business needs
          </p>
          
          {/* Currency Selector */}
          <div className="flex justify-center mb-4">
            <CurrencySelector compact={true} showLabel={false} />
          </div>
          
          <p className="text-sm text-gray-500 mb-8">
            💡 Prices automatically convert to your local currency based on your location
          </p>
          
          <div className="w-24 h-1 bg-[#FF5400] mx-auto rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {packages.map((pkg, index) => (
            <div
              key={pkg.id}
              className={`relative bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 border-0 ${
                pkg.isPopular ? 'ring-2 ring-[#FF5400]' : ''
              } transform hover:-translate-y-2 flex flex-col h-full`}
            >
              {pkg.isPopular && (
                <div className="absolute top-0 right-0 left-0 bg-gradient-to-r from-[#FF5400] to-[#FF7E00] text-white text-center py-3 px-4 text-sm font-medium">
                  <span className="flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    Most Popular
                  </span>
                </div>
              )}

              <div className="p-8 pt-12 flex flex-col flex-grow w-full">
                <div className="flex-grow">
                  <h3 className="text-xl font-semibold text-[#0A2647] mb-2">{pkg.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{pkg.description}</p>

                  <div className="mt-6 mb-8">
                    <SmartPrice 
                      key={`price-${pkg.name}`}
                      kesAmount={pkg.price} 
                      size="xl" 
                      showOriginal={true}
                      className="text-center"
                    />
                  </div>

                  <div className="h-px w-full bg-gray-100 my-6"></div>

                  <h4 className="text-sm font-medium text-[#0A2647] mb-3">What's included:</h4>
                  <div className="max-h-[220px] overflow-y-auto pr-2">
                    <ul className="space-y-3">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                            <svg className="h-3 w-3 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </span>
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="mt-8">
                  <button
                    onClick={() => handleOrderClick(pkg)}
                    className="w-full block text-center px-6 py-3 rounded-xl font-medium text-white transition-all duration-300 shadow-md hover:shadow-lg bg-[#FF5400] hover:bg-[#E54D00] text-sm"
                    aria-label={`Order ${pkg.name} package now`}
                  >
                    <span className="flex items-center justify-center gap-2">
                      Order Now
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {packages.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No packages available at this time.</p>
          </div>
        )}
      </div>

    </section>
  );
}