'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronLeftIcon, ChevronRightIcon, ArrowTopRightOnSquareIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface PortfolioImage {
  id: string;
  src: string;
  alt: string;
  title: string;
  category: string;
  link: string;
}

export default function PortfolioSlider() {
  const [images, setImages] = useState<PortfolioImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure component is mounted before using motion components
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch random portfolio images
  useEffect(() => {
    async function fetchImages() {
      try {
        const response = await fetch('/api/portfolio/random');
        if (response.ok) {
          const data = await response.json();
          setImages(data);
        }
      } catch (error) {
        console.error('Error fetching portfolio images:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchImages();
  }, []);

  // Get slides per view based on screen size
  const getSlidesPerView = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth < 768) return 1; // Mobile
      if (window.innerWidth < 1024) return 2; // Tablet
      return 4; // Desktop - 4 images in a row
    }
    return 4;
  };

  const [slidesPerView, setSlidesPerView] = useState(4);

  // Update slides per view on resize
  useEffect(() => {
    const handleResize = () => {
      setSlidesPerView(getSlidesPerView());
    };

    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate max index based on slides per view
  const maxIndex = Math.max(0, images.length - slidesPerView);

  // Auto-rotate slides
  useEffect(() => {
    if (!isAutoPlaying || images.length === 0 || maxIndex === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex((current) => (current + 1) % (maxIndex + 1));
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, images.length, maxIndex]);

  // Navigation functions
  const goToPrevious = () => {
    setCurrentIndex((current) =>
      current === 0 ? maxIndex : current - 1
    );
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToNext = () => {
    setCurrentIndex((current) => (current + 1) % (maxIndex + 1));
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  if (isLoading) {
    const LoadingContent = () => (
      <div className="text-center mb-16">
        <div className="flex items-center justify-center mb-4">
          <SparklesIcon className="w-8 h-8 text-[#FF5400] mr-3" />
          <h2 className="text-4xl md:text-6xl font-bold text-white">
            Creative Portfolio
          </h2>
          <SparklesIcon className="w-8 h-8 text-[#FF5400] ml-3" />
        </div>
        <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
          Showcasing our finest flyer designs and branding masterpieces
        </p>
      </div>
    );

    return (
      <section className="py-24 bg-[#0A1929] relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white/20 rounded-full"></div>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          {isMounted ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <LoadingContent />
            </motion.div>
          ) : (
            <LoadingContent />
          )}

          <div className="flex justify-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-[#FF5400]/20 border-t-[#FF5400]"></div>
              <div className="absolute inset-0 animate-ping rounded-full h-16 w-16 border-2 border-[#FF5400]/40"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (images.length === 0) {
    return null;
  }

  return (
    <section className="py-24 bg-[#0A1929] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 border border-white/20 rounded-full animate-pulse delay-300"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white/20 rounded-full animate-pulse delay-700"></div>
        <div className="absolute top-1/3 right-1/3 w-20 h-20 border border-white/20 rounded-full animate-pulse delay-500"></div>
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-[#0A1929]/50 to-transparent"></div>

      <div className="container mx-auto px-6 relative z-10">
        {(() => {
          const HeaderContent = () => (
            <div className="text-center mb-20">
              <div className="flex items-center justify-center mb-6">
                <div className="h-px w-16 bg-gradient-to-r from-transparent to-[#FF5400] mr-4"></div>
                <SparklesIcon className="w-8 h-8 text-[#FF5400]" />
                <div className="h-px w-16 bg-gradient-to-l from-transparent to-[#FF5400] ml-4"></div>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 leading-tight">
                Creative <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#FF5400] to-[#FF7A00]">Portfolio</span>
              </h2>




            </div>
          );

          return isMounted ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <HeaderContent />
            </motion.div>
          ) : (
            <HeaderContent />
          );
        })()}

        {/* Slider Container */}
        <div className="relative max-w-7xl mx-auto">
          {/* Navigation Arrows - Only show if there are multiple slides */}
          {maxIndex > 0 && (
            <>
              {isMounted ? (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={goToPrevious}
                  className="absolute left-2 md:-left-6 top-1/2 -translate-y-1/2 z-20 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] hover:from-[#FF7A00] hover:to-[#FF5400] text-white p-3 md:p-4 rounded-full shadow-2xl transition-all duration-300 backdrop-blur-sm border border-white/20"
                  aria-label="Previous images"
                >
                  <ChevronLeftIcon className="w-6 h-6 md:w-7 md:h-7" />
                </motion.button>
              ) : (
                <button
                  onClick={goToPrevious}
                  className="absolute left-2 md:-left-6 top-1/2 -translate-y-1/2 z-20 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] hover:from-[#FF7A00] hover:to-[#FF5400] text-white p-3 md:p-4 rounded-full shadow-2xl transition-all duration-300 backdrop-blur-sm border border-white/20"
                  aria-label="Previous images"
                >
                  <ChevronLeftIcon className="w-6 h-6 md:w-7 md:h-7" />
                </button>
              )}

              {isMounted ? (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={goToNext}
                  className="absolute right-2 md:-right-6 top-1/2 -translate-y-1/2 z-20 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] hover:from-[#FF7A00] hover:to-[#FF5400] text-white p-3 md:p-4 rounded-full shadow-2xl transition-all duration-300 backdrop-blur-sm border border-white/20"
                  aria-label="Next images"
                >
                  <ChevronRightIcon className="w-6 h-6 md:w-7 md:h-7" />
                </motion.button>
              ) : (
                <button
                  onClick={goToNext}
                  className="absolute right-2 md:-right-6 top-1/2 -translate-y-1/2 z-20 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] hover:from-[#FF7A00] hover:to-[#FF5400] text-white p-3 md:p-4 rounded-full shadow-2xl transition-all duration-300 backdrop-blur-sm border border-white/20"
                  aria-label="Next images"
                >
                  <ChevronRightIcon className="w-6 h-6 md:w-7 md:h-7" />
                </button>
              )}
            </>
          )}

          {/* Images Container */}
          <div className="overflow-hidden rounded-3xl bg-white/5 backdrop-blur-sm border border-white/10 p-6">
            <div
              className="flex transition-transform duration-700 ease-out"
              style={{ transform: `translateX(-${currentIndex * (100 / slidesPerView)}%)` }}
            >
              {images.map((image, index) => (
                <div
                  key={image.id}
                  className="w-full md:w-1/2 lg:w-1/4 flex-shrink-0 px-2"
                  style={{ width: `${100 / slidesPerView}%` }}
                >
                  <Link href={image.link} className="group block">
                    <div className="relative aspect-square overflow-hidden rounded-2xl bg-white shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2">
                      {/* Gradient Border Effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-[#FF5400] via-[#FF7A00] to-[#FF5400] rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 p-[2px]">
                        <div className="w-full h-full bg-white rounded-2xl"></div>
                      </div>

                      <div className="relative w-full h-full rounded-2xl overflow-hidden">
                        <Image
                          src={image.src}
                          alt={image.alt}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                          className="object-contain transition-all duration-700 group-hover:scale-110"
                          loading={index < 4 ? "eager" : "lazy"}
                          quality={85}
                        />

                        {/* Modern Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-primary/95 via-primary/60 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                          <div className="absolute bottom-0 left-0 right-0 p-6">
                            <div>
                              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] text-white text-sm font-semibold rounded-full mb-3 shadow-lg">
                                <span className="mr-2">
                                  {image.category === 'Flyer Design' ? '🎨' : '🏢'}
                                </span>
                                {image.category}
                              </div>
                              <h3 className="text-white text-lg font-bold line-clamp-2 mb-2">
                                {image.title}
                              </h3>
                              <div className="flex items-center text-white/80 text-sm">
                                <span>View Project</span>
                                <ArrowTopRightOnSquareIcon className="w-4 h-4 ml-2" />
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Floating Action Button */}
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-110">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-xl border border-white/20">
                            <ArrowTopRightOnSquareIcon className="w-5 h-5 text-primary" />
                          </div>
                        </div>

                        {/* Category Badge */}
                        <div className="absolute top-4 left-4 opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-lg border border-white/20">
                            <span className="text-xs font-semibold text-primary">
                              {image.category === 'Flyer Design' ? '🎨' : '🏢'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Modern Dots Indicator */}
          {maxIndex > 0 && (
            <div className="flex justify-center mt-12 space-x-3">
              {Array.from({ length: maxIndex + 1 }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setCurrentIndex(index);
                    setIsAutoPlaying(false);
                    setTimeout(() => setIsAutoPlaying(true), 10000);
                  }}
                  className={`relative transition-all duration-500 hover:scale-110 ${
                    currentIndex === index
                      ? 'w-8 h-3'
                      : 'w-3 h-3'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                >
                  <div className={`w-full h-full rounded-full transition-all duration-500 ${
                    currentIndex === index
                      ? 'bg-gradient-to-r from-[#FF5400] to-[#FF7A00] shadow-lg'
                      : 'bg-white/40 hover:bg-white/60'
                  }`} />
                  {currentIndex === index && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#FF5400] to-[#FF7A00] animate-pulse" />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>


      </div>
    </section>
  );
}
