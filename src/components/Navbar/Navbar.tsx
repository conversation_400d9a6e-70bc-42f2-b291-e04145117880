'use client';
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { AnimatePresence, motion } from 'framer-motion';

// Import extracted modules
import { Icon } from '@/components/icons';
import type { NavItem, NavDropdownItem } from './types';
import { NAV_ITEMS } from './config/navigationItems';
import { useNavbar } from './hooks/useNavbar';
import { NavLogo, HamburgerMenu, CTAButtons, MegaMenu } from './components';

// Mobile menu component
const MobileMenu = React.memo(function MobileMenu({ 
  isOpen, 
  activeDropdown, 
  setActiveDropdown, 
  onClose 
}: { 
  isOpen: boolean; 
  activeDropdown: string | null;
  setActiveDropdown: (dropdown: string | null) => void;
  onClose: () => void; 
}) {
  const pathname = usePathname();

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.3 }}
        className="lg:hidden bg-white border-t border-gray-200 mobile-menu-container"
      >
        <div className="px-4 py-6 space-y-4">
          {NAV_ITEMS.map((item) => (
            <div key={item.label}>
              {item.dropdown ? (
                <div>
                  <button
                    onClick={() => {
                      setActiveDropdown(activeDropdown === item.label ? null : item.label);
                    }}
                    className="flex items-center justify-between w-full py-3 px-4 text-left font-medium text-gray-900 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Icon name={item.icon || 'briefcase'} className="w-5 h-5 text-gray-600" />
                      <span>{item.label}</span>
                    </div>
                    <Icon 
                      name="chevronDown" 
                      className={`w-4 h-4 text-gray-600 transition-transform ${
                        activeDropdown === item.label ? 'rotate-180' : ''
                      }`} 
                    />
                  </button>
                  
                  <AnimatePresence>
                    {activeDropdown === item.label && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="mt-2 ml-4 space-y-2"
                      >
                        {item.dropdown.map((dropdownItem) => {
                          if ('isGroup' in dropdownItem && dropdownItem.isGroup) {
                            return (
                              <div key={dropdownItem.label} className="py-2">
                                <h4 className="text-sm font-semibold text-gray-700 px-3 py-1 bg-gray-100 rounded">
                                  {dropdownItem.label}
                                </h4>
                                <div className="mt-1 space-y-1">
                                  {dropdownItem.items?.map((service) => (
                                    <Link
                                      key={service.href}
                                      href={service.href || '#'}
                                      onClick={onClose}
                                      className="flex items-center space-x-3 py-2 px-3 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded transition-colors"
                                    >
                                      <Icon name={service.icon || 'star'} className="w-4 h-4" />
                                      <span>{service.label}</span>
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            );
                                                     } else {
                             const nonGroupItem = dropdownItem as NavDropdownItem;
                             return (
                               <Link
                                 key={nonGroupItem.href}
                                 href={nonGroupItem.href || '#'}
                                 onClick={onClose}
                                 className="flex items-center space-x-3 py-3 px-4 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                               >
                                 <Icon name={nonGroupItem.icon || 'grid'} className="w-5 h-5" />
                                 <span>{nonGroupItem.label}</span>
                               </Link>
                             );
                           }
                        })}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <Link
                  href={item.href || '#'}
                  onClick={onClose}
                  className={`flex items-center space-x-3 py-3 px-4 rounded-lg font-medium transition-colors ${
                    pathname === item.href
                      ? 'text-orange-600 bg-orange-50'
                      : 'text-gray-900 hover:text-orange-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon name={item.icon || 'home'} className="w-5 h-5" />
                  <span>{item.label}</span>
                  {item.badge && (
                    <span className="ml-auto px-2 py-1 text-xs font-bold text-white bg-red-500 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              )}
            </div>
          ))}
          
          <div className="pt-4 border-t border-gray-200">
            <CTAButtons isMobile={true} />
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
});

MobileMenu.displayName = 'MobileMenu';

// Main Navbar Component
export default function Navbar() {
  const { 
    isOpen, 
    activeDropdown, 
    scrolled, 
    mounted, 
    headerRef,
    setIsOpen, 
    setActiveDropdown 
  } = useNavbar();

  const pathname = usePathname();

  // Loading state for SSR
  if (!mounted) {
    return (
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse" />
              <div className="hidden sm:block space-y-1">
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
            <div className="hidden lg:flex items-center space-x-1">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
            <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse lg:hidden" />
          </div>
        </div>
      </header>
    );
  }

  return (
    <>
      <header
        ref={headerRef}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          scrolled 
            ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' 
            : 'bg-white/80 backdrop-blur-sm border-b border-gray-100'
        }`}
      >
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="flex justify-between items-center h-20">
            <NavLogo />

            <nav className="hidden lg:flex items-center space-x-1">
              {NAV_ITEMS.map((item) => (
                <div key={item.label} className="relative dropdown-container">
                  {item.dropdown ? (
                    <>
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setActiveDropdown(activeDropdown === item.label ? null : item.label);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            setActiveDropdown(activeDropdown === item.label ? null : item.label);
                          }
                          if (e.key === 'Escape') {
                            setActiveDropdown(null);
                          }
                        }}
                        tabIndex={0}
                        data-dropdown-toggle="true"
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                          activeDropdown === item.label
                            ? 'text-orange-600 bg-orange-50'
                            : 'text-gray-700 hover:text-orange-600 hover:bg-gray-50'
                        }`}
                        aria-expanded={activeDropdown === item.label}
                        aria-controls={`${item.label}-dropdown`}
                      >
                        <Icon name={item.icon || 'briefcase'} className="w-4 h-4" />
                        <span>{item.label}</span>
                        <Icon 
                          name="chevronDown" 
                          className={`w-3 h-3 transition-transform duration-200 ${
                            activeDropdown === item.label ? 'rotate-180' : ''
                          }`} 
                        />
                      </button>
                      {activeDropdown === item.label && (
                        <MegaMenu 
                          item={item} 
                          isOpen={activeDropdown === item.label} 
                          onClose={() => setActiveDropdown(null)} 
                        />
                      )}
                    </>
                  ) : (
                    <Link
                      href={item.href || '#'}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 relative ${
                        pathname === item.href
                          ? 'text-orange-600 bg-orange-50'
                          : 'text-gray-700 hover:text-orange-600 hover:bg-gray-50'
                      }`}
                    >
                      <Icon name={item.icon || 'home'} className="w-4 h-4" />
                      <span>{item.label}</span>
                      {item.badge && (
                        <span className="absolute -top-2 -right-1 px-2 py-1 text-xs font-bold text-white bg-red-500 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </div>
              ))}
            </nav>

            <div className="flex items-center space-x-4">
              <div className="hidden lg:block">
                <CTAButtons />
              </div>
              <HamburgerMenu 
                isOpen={isOpen} 
                onToggle={() => setIsOpen(!isOpen)} 
              />
            </div>
          </div>
        </div>
      </header>

      <MobileMenu 
        isOpen={isOpen} 
        activeDropdown={activeDropdown}
        setActiveDropdown={setActiveDropdown}
        onClose={() => setIsOpen(false)} 
      />
    </>
  );
}
