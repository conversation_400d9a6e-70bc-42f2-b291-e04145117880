import { useState, useEffect, useRef, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import type { NavbarState } from '../types';

export interface UseNavbarResult {
  isOpen: boolean;
  activeDropdown: string | null;
  scrolled: boolean;
  mounted: boolean;
  headerRef: React.RefObject<HTMLElement | null>;
  setIsOpen: (open: boolean) => void;
  setActiveDropdown: (dropdown: string | null) => void;
}

/**
 * Custom hook for navbar state management
 * Extracted from main Navbar component for better separation of concerns
 */
export function useNavbar(): UseNavbarResult {
  const [state, setState] = useState<NavbarState>({
    isOpen: false,
    activeDropdown: null,
    scrolled: false,
    mounted: false
  });

  const pathname = usePathname();
  const headerRef = useRef<HTMLElement>(null);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);

  // Memoized state updates
  const setIsOpen = useCallback((open: boolean) => {
    setState(prev => ({ 
      ...prev, 
      isOpen: open,
      activeDropdown: open ? prev.activeDropdown : null
    }));
  }, []);

  const setActiveDropdown = useCallback((dropdown: string | null) => {
    setState(prev => ({ ...prev, activeDropdown: dropdown }));
  }, []);

  // Close menu on route change
  useEffect(() => {
    setIsOpen(false);
    setActiveDropdown(null);
  }, [pathname, setIsOpen, setActiveDropdown]);

  // Body scroll lock for mobile menu
  useEffect(() => {
    if (state.isOpen) {
      // Prevent body scroll when mobile menu is open
      document.body.classList.add('mobile-menu-open');
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scroll when mobile menu is closed
      document.body.classList.remove('mobile-menu-open');
      document.body.style.overflow = '';
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('mobile-menu-open');
      document.body.style.overflow = '';
    };
  }, [state.isOpen]);

  // Mount detection
  useEffect(() => {
    setState(prev => ({ ...prev, mounted: true }));
  }, []);

  // Enhanced scroll handling with throttling
  useEffect(() => {
    if (!state.mounted) return;

    const handleScroll = () => {
      if (scrollTimeout.current) {
        return;
      }

      scrollTimeout.current = setTimeout(() => {
        const currentScrollY = window.scrollY;
        const isScrolled = currentScrollY > 20;
        
        setState(prev => {
          // Only update if scrolled state actually changed
          if (isScrolled !== prev.scrolled) {
            return { ...prev, scrolled: isScrolled };
          }
          return prev;
        });

        scrollTimeout.current = null;
      }, 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [state.mounted]);

  // Handle escape key to close dropdown
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setActiveDropdown(null);
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [setActiveDropdown, setIsOpen]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      
      // Don't close if clicking on dropdown toggle buttons
      if (target?.closest('[data-dropdown-toggle="true"]')) {
        return;
      }
      
      // Close dropdown if clicking outside the dropdown container
      if (!target?.closest('.dropdown-container')) {
        setActiveDropdown(null);
      }
      
      // Close mobile menu if clicking outside
      if (!target?.closest('.mobile-menu-container') && !target?.closest('[data-mobile-menu-toggle]')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setActiveDropdown, setIsOpen]);

  return {
    isOpen: state.isOpen,
    activeDropdown: state.activeDropdown,
    scrolled: state.scrolled,
    mounted: state.mounted,
    headerRef,
    setIsOpen,
    setActiveDropdown,
  };
} 