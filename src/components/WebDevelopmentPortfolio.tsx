'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { WebsitePortfolioItem } from '@/types/portfolio';

const WebDevelopmentPortfolio = React.memo(function WebDevelopmentPortfolio() {
  const [websitePortfolioItems, setWebsitePortfolioItems] = useState<WebsitePortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchWebsitePortfolioItems = async () => {
      try {
        setLoading(true);
        setError('');

        const response = await fetch(`/api/website-portfolio?t=${Date.now()}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch website portfolio items: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Portfolio items received:', data);
        setWebsitePortfolioItems(data);
      } catch (err) {
        console.error('Error fetching website portfolio items:', err);
        setError('Failed to load website portfolio items. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchWebsitePortfolioItems();
  }, []);

  if (loading) {
    return (
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Work</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-12">Explore our latest web development projects</p>
          <div className="flex justify-center">
            <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          </div>
          <p className="mt-4 text-sm text-gray-500">Loading portfolio items...</p>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Work</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-12">Explore our latest web development projects</p>
          <div className="max-w-md mx-auto p-4 bg-red-50 rounded-lg">
            <p className="text-red-500">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  if (websitePortfolioItems.length === 0) {
    return (
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Work</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-12">Explore our latest web development projects</p>
          <p className="text-gray-500">No portfolio items found. Check back soon for updates.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#FF5400]">
            Web Development Portfolio
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our latest web development projects showcasing modern design and functionality
          </p>
        </div>

        {/* Portfolio Grid - 3 per row */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {websitePortfolioItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
            >
              {/* Image Container */}
              <div className="relative aspect-[4/3] overflow-hidden">
                <Image
                  src={item.imageSrc || '/images/placeholder.jpg'}
                  alt={item.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-cover transition-all duration-500 group-hover:scale-110"
                  loading={index < 3 ? "eager" : "lazy"}
                  quality={75}
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#FF5400] text-white text-xs font-medium rounded-full mb-2">
                          {item.category}
                        </div>
                        <h3 className="text-white text-lg font-semibold line-clamp-1">
                          {item.title}
                        </h3>
                      </div>

                      {/* External link icon */}
                      <div className="ml-4">
                        <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                          <i className="fas fa-external-link-alt w-5 h-5 text-white text-sm"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-block px-3 py-1 bg-[#FF5400]/10 text-[#FF5400] text-xs font-medium rounded-full">
                    {item.category}
                  </span>
                  {item.featured && (
                    <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                      Featured
                    </span>
                  )}
                </div>

                <h3 className="text-xl font-bold mb-3 text-gray-800 line-clamp-1">
                  {item.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {item.description}
                </p>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors text-sm"
                  >
                    Visit Site
                  </a>
                  <Link
                    href={`/web-development?project=${item.id}`}
                    className="flex-1 border border-gray-300 hover:border-[#FF5400] hover:text-[#FF5400] text-gray-700 text-center py-2 px-4 rounded-lg font-medium transition-colors text-sm"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link
            href="/web-development"
            className="inline-block px-8 py-4 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white font-medium rounded-full transition-colors"
          >
            View All Web Projects
          </Link>
        </div>
      </div>
    </section>
  );
});

export default WebDevelopmentPortfolio;
