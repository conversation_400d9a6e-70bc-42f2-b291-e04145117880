'use client';

import { useState, useEffect } from 'react';
import ModernPageHero from '@/components/ModernPageHero';
import ModernProcessSection from '@/components/ModernProcessSection';
import ModernFAQSection from '@/components/ModernFAQSection';

// Digital marketing services data
const digitalMarketingServices = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    ),
    title: 'Search Engine Optimization',
    description: 'Improve your website\'s visibility on search engines and drive organic traffic to your business.',
    features: [
      'Keyword Research & Strategy',
      'On-Page SEO Optimization',
      'Technical SEO Audits',
      'Local SEO for Businesses'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    ),
    title: 'Social Media Marketing',
    description: 'Build your brand presence across social platforms and engage with your target audience effectively.',
    features: [
      'Content Strategy & Planning',
      'Social Media Management',
      'Community Engagement',
      'Influencer Partnerships'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
      </svg>
    ),
    title: 'Pay-Per-Click Advertising',
    description: 'Drive immediate traffic and conversions with targeted Google Ads and social media advertising campaigns.',
    features: [
      'Google Ads Campaigns',
      'Facebook & Instagram Ads',
      'Landing Page Optimization',
      'Conversion Tracking'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
    title: 'Email Marketing',
    description: 'Build lasting relationships with your customers through strategic email campaigns and automation.',
    features: [
      'Email Campaign Design',
      'Marketing Automation',
      'List Building Strategies',
      'Performance Analytics'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
      </svg>
    ),
    title: 'Content Marketing',
    description: 'Create compelling content that attracts, engages, and converts your target audience into loyal customers.',
    features: [
      'Content Strategy Development',
      'Blog Writing & SEO',
      'Video Content Creation',
      'Content Distribution'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    title: 'Analytics & Reporting',
    description: 'Track, measure, and optimize your digital marketing performance with comprehensive analytics and insights.',
    features: [
      'Google Analytics Setup',
      'Performance Dashboards',
      'ROI Measurement',
      'Monthly Reporting'
    ]
  }
];

// Process steps for digital marketing
const processSteps = [
  {
    step: '1',
    title: 'Strategy & Planning',
    description: 'We analyze your business, competitors, and target audience to create a comprehensive digital marketing strategy.'
  },
  {
    step: '2',
    title: 'Campaign Setup',
    description: 'Set up and configure all necessary tools, platforms, and tracking systems for your marketing campaigns.'
  },
  {
    step: '3',
    title: 'Content Creation',
    description: 'Develop engaging content including graphics, videos, copy, and landing pages optimized for conversions.'
  },
  {
    step: '4',
    title: 'Launch & Optimize',
    description: 'Launch campaigns across selected channels and continuously optimize based on performance data.'
  },
  {
    step: '5',
    title: 'Monitor & Report',
    description: 'Track performance metrics, provide detailed reports, and make data-driven recommendations for improvement.'
  }
];

// FAQ data
const faqData = [
  {
    question: "How long does it take to see results from digital marketing?",
    answer: "Results vary by channel and strategy. PPC ads can show immediate results, while SEO typically takes 3-6 months. Social media and content marketing usually show significant results within 2-4 months of consistent effort."
  },
  {
    question: "What's included in your digital marketing packages?",
    answer: "Our packages include strategy development, campaign setup and management, content creation, performance tracking, and monthly reporting. Specific services vary by package level and can be customized to your needs."
  },
  {
    question: "Do you work with businesses of all sizes?",
    answer: "Yes, we work with startups, small businesses, and large enterprises. Our strategies and packages are scalable and can be customized to fit your budget and business goals."
  },
  {
    question: "How do you measure the success of digital marketing campaigns?",
    answer: "We track key performance indicators (KPIs) including website traffic, conversion rates, cost per acquisition, return on ad spend (ROAS), and overall ROI. We provide detailed monthly reports with actionable insights."
  },
  {
    question: "Can you help with both organic and paid marketing strategies?",
    answer: "Absolutely! We offer comprehensive services including organic strategies like SEO and content marketing, as well as paid advertising through Google Ads, social media ads, and other platforms for maximum reach and impact."
  }
];

export default function DigitalMarketingPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handlePackageSelect = (packageName: string, price: string) => {
    const whatsappMessage = `Hello Mocky Digital! I'm interested in the ${packageName} package (KES ${price}). Please provide more information about digital marketing services.`;
    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!mounted) {
    return null;
  }

  return (
    <main>
      {/* Hero Section */}
      <ModernPageHero
        title="Digital Marketing Solutions"
        description="Grow your business online with data-driven digital marketing strategies. From SEO to social media, we help you reach your target audience and achieve measurable results."
      />

      {/* Value Proposition Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">
              WHY DIGITAL MARKETING
            </span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
              Transform Your Online Presence
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              In today's digital world, your online presence determines your business success. 
              We help you leverage the power of digital marketing to reach more customers, increase sales, and grow your brand.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                title: 'Increased Visibility',
                description: 'Get found by your target audience when they\'re searching for your products or services'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                title: 'Targeted Reach',
                description: 'Reach the right people at the right time with precision-targeted marketing campaigns'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                ),
                title: 'Measurable Results',
                description: 'Track every click, conversion, and sale with detailed analytics and performance reports'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                ),
                title: 'Cost-Effective',
                description: 'Get better ROI compared to traditional marketing with optimized digital strategies'
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-4 text-[#FF5400]">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">
              OUR SERVICES
            </span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
              Comprehensive Digital Marketing Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From strategy to execution, we provide end-to-end digital marketing services that drive real business results.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {digitalMarketingServices.map((service, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300"
              >
                <div className="flex justify-center mb-6 text-[#FF5400]">{service.icon}</div>
                <h3 className="text-xl font-bold mb-4 text-gray-900">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Digital Marketing Impact Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="lg:pr-8">
              <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-4 shadow-sm">
                DIGITAL IMPACT
              </span>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900 leading-tight">
                The Power of Digital Marketing
              </h2>
              <p className="text-lg text-gray-600 mb-10 leading-relaxed">
                Digital marketing isn't just a trend—it's the future of business growth. 
                With the right strategy, you can reach global audiences, build meaningful relationships, and drive sustainable growth.
              </p>

              {/* Statistics */}
              <div className="space-y-8">
                {[
                  {
                    stat: '4.8B',
                    description: 'people use the internet worldwide, creating massive opportunities for businesses',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                    )
                  },
                  {
                    stat: '89%',
                    description: 'of consumers research products online before making purchase decisions',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    )
                  },
                  {
                    stat: '200%',
                    description: 'average ROI that businesses see from effective email marketing campaigns',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    )
                  }
                ].map((stat, index) => (
                  <div key={index} className="flex items-start gap-6 p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#FF5400]/10 rounded-lg flex items-center justify-center text-[#FF5400]">
                        {stat.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="text-3xl font-bold text-[#FF5400] mb-2">{stat.stat}</div>
                      <div className="text-gray-600 leading-relaxed">{stat.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Visual Elements */}
            <div className="relative lg:pl-8">
              <div className="relative">
                {/* Background decorative elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#FF5400]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-[#0A2647]/10 rounded-full blur-xl"></div>
                
                {/* Main grid */}
                <div className="grid grid-cols-2 gap-6 relative z-10">
                  {[
                    { 
                      title: 'SEO', 
                      color: 'bg-[#FF5400]',
                      textColor: 'text-white',
                      description: 'Organic Traffic',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Social Media', 
                      color: 'bg-[#0A2647]',
                      textColor: 'text-white',
                      highlightColor: 'text-[#FF5400]',
                      description: 'Brand Engagement',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      )
                    },
                    { 
                      title: 'PPC Ads', 
                      color: 'bg-gray-800',
                      textColor: 'text-white',
                      highlightColor: 'text-[#FF5400]',
                      description: 'Paid Traffic',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Analytics', 
                      color: 'bg-gradient-to-br from-[#FF5400] to-[#e84a00]',
                      textColor: 'text-white',
                      description: 'Data Insights',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      )
                    }
                  ].map((element, index) => (
                    <div
                      key={index}
                      className={`${element.color} ${element.textColor} p-8 rounded-2xl hover:scale-105 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl`}
                    >
                      <div className="flex flex-col items-center text-center space-y-3">
                        <div className="text-white">{element.icon}</div>
                        <div>
                          <h3 className={`font-bold text-lg mb-1 ${element.highlightColor || 'text-white'}`}>{element.title}</h3>
                          <p className="text-sm text-white/90">{element.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">
              PRICING PLANS
            </span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
              Digital Marketing Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the perfect package for your business needs. All packages include strategy development, implementation, and monthly reporting.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                name: 'Starter Digital',
                price: '35,000',
                period: '/month',
                description: 'Perfect for small businesses starting their digital journey',
                features: [
                  'Social Media Management (2 platforms)',
                  'Basic SEO Setup',
                  'Google My Business Optimization',
                  'Monthly Performance Report',
                  'Email Support'
                ],
                popular: false,
                buttonText: 'Get Started',
                buttonStyle: 'bg-gray-900 hover:bg-gray-800 text-white'
              },
              {
                name: 'Growth Digital',
                price: '65,000',
                period: '/month',
                description: 'Comprehensive marketing for growing businesses',
                features: [
                  'Social Media Management (4 platforms)',
                  'Complete SEO Strategy',
                  'Google Ads Management (KES 20K budget)',
                  'Email Marketing Campaigns',
                  'Content Creation (8 posts/month)',
                  'Bi-weekly Strategy Calls',
                  'Priority Support'
                ],
                popular: true,
                buttonText: 'Most Popular',
                buttonStyle: 'bg-[#FF5400] hover:bg-[#e84a00] text-white'
              },
              {
                name: 'Enterprise Digital',
                price: '120,000',
                period: '/month',
                description: 'Full-scale marketing for established businesses',
                features: [
                  'Multi-Platform Social Media',
                  'Advanced SEO & Content Marketing',
                  'Google & Facebook Ads (KES 50K budget)',
                  'Marketing Automation',
                  'Video Content Creation',
                  'Landing Page Development',
                  'Weekly Strategy Sessions',
                  'Dedicated Account Manager'
                ],
                popular: false,
                buttonText: 'Contact Us',
                buttonStyle: 'bg-gray-900 hover:bg-gray-800 text-white'
              }
            ].map((pkg, index) => (
              <div
                key={index}
                className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${
                  pkg.popular ? 'border-2 border-[#FF5400] transform scale-105' : 'border border-gray-200'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#FF5400] text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                  <p className="text-gray-600 mb-6">{pkg.description}</p>
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-gray-900">KES {pkg.price}</span>
                    <span className="text-gray-600">{pkg.period}</span>
                  </div>
                  <ul className="space-y-3 mb-8">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <svg className="w-5 h-5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handlePackageSelect(pkg.name, pkg.price)}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${pkg.buttonStyle}`}
                  >
                    {pkg.buttonText}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <ModernProcessSection
        title="Our Digital Marketing Process"
        description="From strategy to execution, we follow a proven process that delivers results"
        steps={processSteps}
      />

      {/* CTA Section */}
      <section className="py-20 bg-[#0A2647]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Grow Your Business Online?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's create a digital marketing strategy that drives real results for your business. 
            Contact us today for a free consultation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => {
                const whatsappMessage = "Hello Mocky Digital! I'm interested in your digital marketing services. Please provide more information.";
                const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
                window.open(whatsappUrl, '_blank');
              }}
              className="bg-[#FF5400] hover:bg-[#e84a00] text-white px-8 py-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center gap-3"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.306"/>
              </svg>
              Start Your Digital Journey
            </button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <ModernFAQSection
        faqs={faqData}
      />
    </main>
  );
} 