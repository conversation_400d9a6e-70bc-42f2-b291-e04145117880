import { ClockIcon, TruckIcon, MapPinIcon, CheckCircleIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { 
  getUnitDisplay, 
  getProductionCost, 
  getDesignFee,
  type PricingItem 
} from '@/utils/pricing';

interface PricingCalculatorProps {
  product: PricingItem | null;
  quantity: string;
  meters: string;
  orderType: 'design' | 'print' | 'both';
  onQuantityChange: (value: string) => void;
  onMetersChange: (value: string) => void;
  onOrderTypeChange: (type: 'design' | 'print' | 'both') => void;
  onGetQuote: () => void;
  loading?: boolean;
}

/**
 * Interactive pricing calculator with order type selection
 * Extracted from main product page for better modularity
 */
export default function PricingCalculator({
  product,
  quantity,
  meters,
  orderType,
  onQuantityChange,
  onMetersChange,
  onOrderTypeChange,
  onGetQuote,
  loading = false
}: PricingCalculatorProps) {
  if (loading || !product) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 space-y-4">
        <div className="h-6 bg-gray-200 rounded animate-pulse" />
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="h-12 bg-gray-200 rounded animate-pulse" />
      </div>
    );
  }

  const actualQuantity = parseInt(quantity) || 1;
  const actualMeters = parseFloat(meters) || 1;
  const isMetreBased = product.pricingType === 'per_meter';
  const unitDisplay = getUnitDisplay(product);

  // Calculate pricing based on order type
  const designFee = (orderType === 'design' || orderType === 'both') ? getDesignFee(product) : 0;
  const printCost = (orderType === 'print' || orderType === 'both') ? getProductionCost(product, quantity, meters) : 0;
  const totalAmount = designFee + printCost;

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 lg:p-8 space-y-8 sticky top-36">
      {/* Enhanced Header */}
      <div className="text-center pb-4 border-b border-gray-100">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 rounded-full px-4 py-2 mb-3">
          <SparklesIcon className="w-4 h-4 text-orange-600" />
          <span className="text-orange-800 font-semibold text-sm">CUSTOMIZE YOUR ORDER</span>
        </div>
        <h3 className="text-xl font-bold text-gray-900">Select Service Type</h3>
        <p className="text-gray-600 text-sm mt-1">Choose the service that best fits your needs</p>
      </div>

      {/* Enhanced Service Type Selection */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <button
            onClick={() => onOrderTypeChange('design')}
            className={`group p-5 rounded-xl border-2 transition-all duration-300 text-left hover:shadow-lg ${
              orderType === 'design'
                ? 'border-orange-500 bg-gradient-to-br from-orange-50 to-red-50 shadow-md'
                : 'border-gray-200 hover:border-orange-300 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${orderType === 'design' ? 'bg-orange-500' : 'bg-gray-300'}`} />
                  <div className="font-semibold text-gray-900">Design Only</div>
                </div>
                <div className="text-sm text-gray-600 mb-3">Get a custom design (no printing)</div>
                <div className="text-lg font-bold text-orange-600">
                  KSh {getDesignFee(product).toLocaleString()}
                </div>
              </div>
              {orderType === 'design' && (
                <CheckCircleIcon className="w-6 h-6 text-orange-500 flex-shrink-0" />
              )}
            </div>
          </button>

          <button
            onClick={() => onOrderTypeChange('print')}
            className={`group p-5 rounded-xl border-2 transition-all duration-300 text-left hover:shadow-lg ${
              orderType === 'print'
                ? 'border-orange-500 bg-gradient-to-br from-orange-50 to-red-50 shadow-md'
                : 'border-gray-200 hover:border-orange-300 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${orderType === 'print' ? 'bg-orange-500' : 'bg-gray-300'}`} />
                  <div className="font-semibold text-gray-900">Print Only</div>
                </div>
                <div className="text-sm text-gray-600 mb-3">Bring your own design to print</div>
                <div className="text-lg font-bold text-orange-600">
                  {isMetreBased
                    ? `KSh ${(product.pricePerMeter || 0).toLocaleString()}/${unitDisplay.singular}`
                    : `KSh ${(product.price || 0).toLocaleString()}/${unitDisplay.singular}`
                  }
                </div>
              </div>
              {orderType === 'print' && (
                <CheckCircleIcon className="w-6 h-6 text-orange-500 flex-shrink-0" />
              )}
            </div>
          </button>

          <button
            onClick={() => onOrderTypeChange('both')}
            className={`group p-5 rounded-xl border-2 transition-all duration-300 text-left hover:shadow-lg relative ${
              orderType === 'both'
                ? 'border-orange-500 bg-gradient-to-br from-orange-50 to-red-50 shadow-md'
                : 'border-gray-200 hover:border-orange-300 hover:bg-gray-50'
            }`}
          >
            {/* Popular badge */}
            <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              POPULAR
            </div>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${orderType === 'both' ? 'bg-orange-500' : 'bg-gray-300'}`} />
                  <div className="font-semibold text-gray-900">Design + Print</div>
                </div>
                <div className="text-sm text-gray-600 mb-3">Full service: design and printing</div>
                <div className="text-lg font-bold text-orange-600">
                  Complete package
                </div>
              </div>
              {orderType === 'both' && (
                <CheckCircleIcon className="w-6 h-6 text-orange-500 flex-shrink-0" />
              )}
            </div>
          </button>
        </div>
      </div>

      {/* Enhanced Quantity/Size Input */}
      {(orderType === 'print' || orderType === 'both') && (
        <div className="space-y-4 p-5 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200">
          <label className="block text-sm font-bold text-gray-900 flex items-center gap-2">
            <div className="w-1 h-4 bg-gradient-to-b from-orange-500 to-red-500 rounded-full" />
            {isMetreBased ? `Size (${unitDisplay.plural})` : `Quantity (${unitDisplay.plural})`}
          </label>
          {isMetreBased ? (
            <div className="relative">
              <input
                type="number"
                min="0.1"
                step="0.1"
                value={meters}
                onChange={(e) => onMetersChange(e.target.value)}
                className="w-full px-4 py-4 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 text-lg font-semibold"
                placeholder={`Enter ${unitDisplay.plural}`}
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                {unitDisplay.plural}
              </div>
            </div>
          ) : (
            <div className="relative">
              <input
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => onQuantityChange(e.target.value)}
                className="w-full px-4 py-4 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 text-lg font-semibold"
                placeholder={`Enter number of ${unitDisplay.plural}`}
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                {unitDisplay.plural}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Enhanced Price Breakdown */}
      <div className="space-y-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-200">
        <h4 className="font-bold text-gray-900 flex items-center gap-2 text-lg">
          <div className="w-1 h-5 bg-gradient-to-b from-orange-500 to-red-500 rounded-full" />
          Price Breakdown
        </h4>

        {(orderType === 'design' || orderType === 'both') && (
          <div className="flex justify-between items-center py-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
              <span className="text-gray-700 font-medium">Design Fee (one-time)</span>
            </div>
            <span className="font-bold text-gray-900">KSh {designFee.toLocaleString()}</span>
          </div>
        )}

        {(orderType === 'print' || orderType === 'both') && (
          <div className="flex justify-between items-center py-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
              <span className="text-gray-700 font-medium">
                Production Cost ({isMetreBased ? actualMeters : actualQuantity} {unitDisplay.plural})
              </span>
            </div>
            <span className="font-bold text-gray-900">KSh {printCost.toLocaleString()}</span>
          </div>
        )}

        <div className="border-t-2 border-orange-200 pt-4 mt-4">
          <div className="flex justify-between items-center">
            <span className="font-bold text-gray-900 text-lg">Total Amount</span>
            <div className="text-right">
              <div className="font-black text-2xl text-orange-600">
                KSh {totalAmount.toLocaleString()}
              </div>
              <div className="text-xs text-gray-600 font-medium">
                All inclusive
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Service Info */}
      <div className="space-y-4 p-5 bg-white rounded-xl border border-gray-200 shadow-sm">
        <h4 className="font-bold text-gray-900 flex items-center gap-2">
          <div className="w-1 h-4 bg-gradient-to-b from-orange-500 to-red-500 rounded-full" />
          Service Benefits
        </h4>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center">
              <ClockIcon className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">Fast Turnaround</div>
              <div className="text-sm text-gray-600">2-5 business days delivery</div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
              <TruckIcon className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">Free Delivery</div>
              <div className="text-sm text-gray-600">Within Nairobi CBD</div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center">
              <MapPinIcon className="w-4 h-4 text-purple-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">Studio Pickup</div>
              <div className="text-sm text-gray-600">Available at our location</div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Get Quote Button */}
      <div className="space-y-3">
        <button
          onClick={onGetQuote}
          className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-5 px-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <div className="flex items-center justify-center gap-2">
            <span className="text-lg">Order Now</span>
            <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </div>
        </button>
        <p className="text-center text-xs text-gray-500">
          No commitment required • Get instant quote
        </p>
      </div>
    </div>
  );
} 