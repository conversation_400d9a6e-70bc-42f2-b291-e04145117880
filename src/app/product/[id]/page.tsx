'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeftIcon, HomeIcon } from '@heroicons/react/24/outline';
import ArtworkUpload from '@/components/ArtworkUpload';
import OrderFormModal from '@/components/OrderFormModal';
import {
  getUnitDisplay,
  getProductionCost,
  getDesignFee
} from '@/utils/pricing';
import { useProductData, useOrderForm } from './hooks';
import { ImageGallery, ProductInfo, PricingCalculator } from './components';

interface ProductDetailProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProductDetailPage({ params }: ProductDetailProps) {
  const router = useRouter();
  
  // Use extracted hooks for cleaner state management
  const { product, relatedProducts, loading, error, productId } = useProductData(params);
  const orderForm = useOrderForm(product);
  
  // Simple UI state
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const images = [
    product?.imageUrl,
    product?.imageUrl2,
    product?.imageUrl3
  ].filter(Boolean);

  const handleWhatsAppClick = (artworkUrls: string[] = []) => {
    const actualQuantity = parseInt(orderForm.quantity) || 1;
    const actualMeters = parseFloat(orderForm.meters) || 1;
    const isDesignOnlyOrder = orderForm.orderType === 'design';
    const isPrintOnlyOrder = orderForm.orderType === 'print';
    const isBothOrder = orderForm.orderType === 'both';
    const isMetreBased = product?.pricingType === 'per_meter';

    // Calculate pricing
    const designFee = (isDesignOnlyOrder || isBothOrder) ? getDesignFee(product) : 0;
    const printCost = (isPrintOnlyOrder || isBothOrder) ? getProductionCost(product, orderForm.quantity, orderForm.meters) : 0;
    const totalAmount = designFee + printCost;

    let message = `Hi! I'm interested in: ${product?.service}\n\n`;
    
    // Order type
    let serviceType = '';
    if (isDesignOnlyOrder) {
      serviceType = 'Design Only (no printing)';
    } else if (isPrintOnlyOrder) {
      serviceType = 'Print Only (bring your own design)';
    } else if (isBothOrder) {
      serviceType = 'Design + Print (full service)';
    }
    message += `Service Type: ${serviceType}\n\n`;
    
    if (orderForm.designBrief) {
      message += `Design Brief: ${orderForm.designBrief}\n\n`;
    }
    
    if (isPrintOnlyOrder || isBothOrder) {
      const unitDisplay = getUnitDisplay(product);
      if (isMetreBased) {
        message += `Size: ${actualMeters} ${unitDisplay.plural}\n\n`;
      } else {
        message += `Quantity: ${actualQuantity.toLocaleString()} ${unitDisplay.plural}\n\n`;
      }
    }
    
    if (orderForm.uploadedArtwork.length > 0) {
      message += `I have ${orderForm.uploadedArtwork.length} artwork file(s) to share.\n\n`;
    }
    
    // Price breakdown
    message += `💰 PRICE BREAKDOWN:\n`;
    
    if (isPrintOnlyOrder || isBothOrder) {
      const unitDisplay = getUnitDisplay(product);
      if (isMetreBased) {
        message += `Production Cost (${actualMeters} ${unitDisplay.plural} × KSh ${(product?.pricePerMeter || 0).toLocaleString()}): KSh ${printCost.toLocaleString()}\n`;
      } else {
        message += `Production Cost (${actualQuantity} ${unitDisplay.plural} × KSh ${(product?.price || 0).toLocaleString()}): KSh ${printCost.toLocaleString()}\n`;
      }
    }
    
    if (isDesignOnlyOrder || isBothOrder) {
      message += `Design Fee (one-time): KSh ${designFee.toLocaleString()}\n`;
    }
    message += `TOTAL: KSh ${totalAmount.toLocaleString()}\n\n`;

    // Add artwork URLs if available
    if (artworkUrls && artworkUrls.length > 0) {
      message += `📎 ARTWORK FILES:\n`;
      artworkUrls.forEach((url, index) => {
        message += `${index + 1}. ${url}\n`;
      });
      message += `\n`;
    }

    // Payment details
    message += `💳 M-PESA PAYMENT DETAILS:\n`;
    message += `Business Number: 522533\n`;
    message += `Account Number: 7934479\n`;
    message += `Amount: KSh ${totalAmount.toLocaleString()}\n\n`;

    message += `I'm ready to proceed with this order and make the M-Pesa deposit of KSh ${totalAmount.toLocaleString()}. Please confirm and provide the project timeline. Thank you!`;

    const whatsappUrl = `https://wa.me/************?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleShare = async () => {
    const shareData = {
      title: product?.service || 'Product',
      text: product?.description || 'Check out this product',
      url: window.location.href,
    };
    
    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (err) {
        copyToClipboard(window.location.href);
      }
    } else {
      copyToClipboard(window.location.href);
    }
  };

  const copyToClipboard = async (text: string, label: string = 'Text') => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      
      // Show success notification
      orderForm.setNotification({
        type: 'success',
        message: `${label} copied to clipboard!`
      });
      
      // Clear the copied text indicator after 2 seconds
      setTimeout(() => {
        setCopiedText(null);
      }, 2000);
      
    } catch (err) {
      console.error('Failed to copy text: ', err);
      orderForm.setNotification({
        type: 'error',
        message: `Failed to copy ${label.toLowerCase()}. Please try again.`
      });
    }
  };

  const handleGetQuote = () => {
    // Redirect to the order page instead of showing the modal
    router.push(`/order/${product?.id}`);
  };

  const handleOrderSubmit = async () => {
    // Validate customer info (name and phone are required, email is optional)
    if (!orderForm.customerInfo.name || !orderForm.customerInfo.phone) {
      alert('Please provide your name and phone number to continue.');
      return;
    }

    setIsSubmitting(true);
    try {
      const actualQuantity = parseInt(orderForm.quantity) || 1;
      const actualMeters = parseFloat(orderForm.meters) || 1;
      const isDesignOnly = orderForm.orderType === 'design';
      const isPrintOnly = orderForm.orderType === 'print';
      const isBoth = orderForm.orderType === 'both';
      const isMetreBased = product?.pricingType === 'per_meter';
      
      // Calculate design fee and total amount
      const designFee = (isDesignOnly || isBoth) ? getDesignFee(product) : 0;
      const printSubtotal = (isPrintOnly || isBoth) ? getProductionCost(product, orderForm.quantity, orderForm.meters) : 0;
      const totalAmount = designFee + printSubtotal;
      
      const orderData = {
        productId: parseInt(product?.id || '0') || 0,
        productName: product?.service,
        customerName: orderForm.customerInfo.name,
        email: orderForm.customerInfo.email,
        phone: orderForm.customerInfo.phone,
        quantity: isDesignOnly ? 1 : actualQuantity,
        meters: isMetreBased ? actualMeters : null,
        designBrief: orderForm.designBrief,
        artworkFiles: orderForm.uploadedArtwork,
        designOnly: isDesignOnly,
        orderType: isDesignOnly ? 'design_only' : isPrintOnly ? 'print_only' : 'design_and_print',
        unitPrice: product?.price || 0,
        designFee: designFee,
        subtotal: printSubtotal,
        totalAmount: totalAmount,
        notes: orderForm.customerInfo.notes,
        status: 'pending'
      };

      // Submit order to API
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (response.ok) {
        const result = await response.json();
        orderForm.setNotification({
          type: 'success',
          message: 'Order submitted successfully! We will contact you soon.'
        });

        // Clear form after successful submission
        orderForm.setQuantity('1');
        orderForm.setMeters('1');
        orderForm.setOrderType('both');
        orderForm.setDesignBrief('');
        orderForm.setCustomerInfo({
          name: '',
          phone: '',
          email: '',
          notes: ''
        });
        orderForm.setUploadedArtwork([]);
        orderForm.setShowOrderForm(false);

        // Redirect to WhatsApp for immediate communication with artwork URLs
        const artworkUrls = result.order?.artworkUrls || [];
        setTimeout(() => {
          handleWhatsAppClick(artworkUrls);
        }, 2000);
      } else {
        throw new Error('Failed to submit order');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      orderForm.setNotification({
        type: 'error',
        message: 'Failed to submit order. Please try again or contact us directly.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div className="space-y-4">
                <div className="aspect-square bg-gray-200 rounded-xl animate-pulse" />
                <div className="flex space-x-2">
                  {[1,2,3].map(i => (
                    <div key={i} className="w-20 h-20 bg-gray-200 rounded-lg animate-pulse" />
                  ))}
                </div>
              </div>
              <div className="space-y-6">
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-32 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
          <Link
            href="/services"
            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Browse All Services
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-orange-50/20">
        {/* Enhanced Header with Navigation - Fixed positioning */}
        <div className="bg-white/95 backdrop-blur-sm border-b border-gray-200/50 sticky top-16 z-40 mt-16">
          <div className="container mx-auto px-4 py-4">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-between">
                {/* Back Navigation */}
                <div className="flex items-center space-x-4">
                  <Link
                    href="/catalogue"
                    className="group inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 bg-white hover:bg-gray-50 border border-gray-300 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
                  >
                    <ArrowLeftIcon className="w-4 h-4 mr-2 group-hover:-translate-x-0.5 transition-transform duration-200" />
                    Back to Catalogue
                  </Link>

                  {/* Breadcrumb */}
                  <nav className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                    <Link href="/" className="hover:text-gray-700 transition-colors">
                      <HomeIcon className="w-4 h-4" />
                    </Link>
                    <span>/</span>
                    <Link href="/catalogue" className="hover:text-gray-700 transition-colors">
                      Catalogue
                    </Link>
                    <span>/</span>
                    <span className="text-gray-900 font-medium truncate max-w-32">
                      {product?.service || 'Product'}
                    </span>
                  </nav>
                </div>

                {/* Quick Actions */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleShare}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                    aria-label="Share product"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content with Enhanced Layout */}
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16">
              {/* Left Column: Image Gallery */}
              <div className="space-y-6">
                <ImageGallery
                  images={images}
                  productName={product?.service || 'Product'}
                />
              </div>

              {/* Right Column: Product Info & Pricing */}
              <div className="space-y-8 lg:space-y-10">
                <ProductInfo
                  product={product || { service: '', description: '' }}
                  onShare={handleShare}
                  loading={loading}
                />

                <PricingCalculator
                  product={product}
                  quantity={orderForm.quantity}
                  meters={orderForm.meters}
                  orderType={orderForm.orderType}
                  onQuantityChange={orderForm.setQuantity}
                  onMetersChange={orderForm.setMeters}
                  onOrderTypeChange={orderForm.setOrderType}
                  onGetQuote={handleGetQuote}
                  loading={loading}
                />
              </div>
            </div>

            {/* Enhanced Related Products Section */}
            {relatedProducts && relatedProducts.length > 0 && (
              <div className="mt-16 lg:mt-20">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">You Might Also Like</h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Discover more services that complement your current selection
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
                  {relatedProducts.map((relatedProduct, index) => (
                    <Link
                      key={relatedProduct.id}
                      href={`/product/${relatedProduct.id}`}
                      className="group bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-orange-200"
                      style={{
                        animationDelay: `${index * 100}ms`,
                        opacity: 0,
                        animation: 'fadeInUp 0.6s ease-out forwards'
                      }}
                    >
                      <div className="aspect-square relative bg-gradient-to-br from-gray-50 to-gray-100">
                        {relatedProduct.imageUrl && (
                          <Image
                            src={relatedProduct.imageUrl}
                            alt={relatedProduct.service}
                            fill
                            className="object-cover group-hover:scale-110 transition-transform duration-300"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                          />
                        )}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                      <div className="p-5">
                        <h3 className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors mb-2 line-clamp-1">
                          {relatedProduct.service}
                        </h3>
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2 leading-relaxed">
                          {relatedProduct.description}
                        </p>
                        {relatedProduct.price && (
                          <div className="flex items-center justify-between">
                            <p className="text-orange-600 font-bold text-lg">
                              KSh {relatedProduct.price.toLocaleString()}
                            </p>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              From
                            </span>
                          </div>
                        )}
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Notification */}
      {orderForm.notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          orderForm.notification.type === 'success' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
        }`}>
          {orderForm.notification.message}
        </div>
      )}
    </>
  );
} 