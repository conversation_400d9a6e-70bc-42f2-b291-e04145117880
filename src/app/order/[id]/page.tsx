'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { DocumentIcon, PhotoIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import ArtworkUpload from '@/components/ArtworkUpload';
import { getUnitDisplay, getProductionCost, getDesignFee } from '@/utils/pricing';
import { useProductData, useOrderForm } from '@/app/product/[id]/hooks';

interface OrderPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function OrderPage({ params }: OrderPageProps) {
  const router = useRouter();
  // Pass the Promise directly to useProductData, which handles unwrapping internally
  const { product, loading, error } = useProductData(params);
  const orderForm = useOrderForm(product);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!orderForm.customerInfo.name || !orderForm.customerInfo.phone) {
      orderForm.setNotification({
        type: 'error',
        message: 'Please provide your name and phone number to continue.'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const actualQuantity = parseInt(orderForm.quantity) || 1;
      const actualMeters = parseFloat(orderForm.meters) || 1;
      const isDesignOnly = orderForm.orderType === 'design';
      const isPrintOnly = orderForm.orderType === 'print';
      const isBoth = orderForm.orderType === 'both';
      const isMetreBased = product?.pricingType === 'per_meter';
      
      // Calculate design fee and total amount
      const designFee = (isDesignOnly || isBoth) ? getDesignFee(product) : 0;
      const printSubtotal = (isPrintOnly || isBoth) ? getProductionCost(product, orderForm.quantity, orderForm.meters) : 0;
      const totalAmount = designFee + printSubtotal;
      
      const orderData = {
        productId: parseInt(product?.id || '0') || 0,
        productName: product?.service,
        customerName: orderForm.customerInfo.name,
        email: orderForm.customerInfo.email,
        phone: orderForm.customerInfo.phone,
        quantity: isDesignOnly ? 1 : actualQuantity,
        meters: isMetreBased ? actualMeters : null,
        designBrief: orderForm.designBrief,
        artworkFiles: orderForm.uploadedArtwork,
        designOnly: isDesignOnly,
        orderType: isDesignOnly ? 'design_only' : isPrintOnly ? 'print_only' : 'design_and_print',
        unitPrice: product?.price || 0,
        designFee: designFee,
        subtotal: printSubtotal,
        totalAmount: totalAmount,
        notes: orderForm.customerInfo.notes,
        status: 'pending'
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (response.ok) {
        const result = await response.json();
        orderForm.setNotification({
          type: 'success',
          message: 'Order submitted successfully! We will contact you soon.'
        });

        // Clear form
        orderForm.setQuantity('1');
        orderForm.setMeters('1');
        orderForm.setOrderType('print');
        orderForm.setDesignBrief('');
        orderForm.setCustomerInfo({
          name: '',
          phone: '',
          email: '',
          notes: ''
        });
        orderForm.setUploadedArtwork([]);

        // Redirect to WhatsApp with artwork URLs
        const artworkUrls = result.order?.artworkUrls || [];
        const message = generateWhatsAppMessage(orderData, product, artworkUrls);
        window.open(`https://wa.me/254741590670?text=${encodeURIComponent(message)}`, '_blank');

        // Redirect back to product page
        router.push(`/product/${product?.id}?success=true`);
      } else {
        throw new Error('Failed to submit order');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      orderForm.setNotification({
        type: 'error',
        message: 'Failed to submit order. Please try again or contact us directly.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateWhatsAppMessage = (orderData: any, product: any, artworkUrls: string[] = []) => {
    let message = `Hi! I'm interested in: ${product?.service}\n\n`;

    // Order type
    let serviceType = '';
    if (orderData.designOnly) {
      serviceType = 'Design Only (no printing)';
    } else if (orderData.orderType === 'print_only') {
      serviceType = 'Print Only (bring your own design)';
    } else {
      serviceType = 'Design + Print (full service)';
    }
    message += `Service Type: ${serviceType}\n\n`;

    if (orderData.designBrief) {
      message += `Design Brief: ${orderData.designBrief}\n\n`;
    }
    
    if (!orderData.designOnly) {
      const unitDisplay = getUnitDisplay(product);
      if (product?.pricingType === 'per_meter') {
        message += `Size: ${orderData.meters} ${unitDisplay.plural}\n\n`;
      } else {
        message += `Quantity: ${orderData.quantity.toLocaleString()} ${unitDisplay.plural}\n\n`;
      }
    }
    
    if (orderData.artworkFiles.length > 0) {
      message += `I have ${orderData.artworkFiles.length} artwork file(s) to share.\n\n`;
    }
    
    // Price breakdown
    message += `💰 PRICE BREAKDOWN:\n`;
    
    if (!orderData.designOnly) {
      message += `Production Cost: KSh ${orderData.subtotal.toLocaleString()}\n`;
    }
    
    if (orderData.designFee > 0) {
      message += `Design Fee (one-time): KSh ${orderData.designFee.toLocaleString()}\n`;
    }
    message += `TOTAL: KSh ${orderData.totalAmount.toLocaleString()}\n\n`;

    // Add artwork URLs if available
    if (artworkUrls && artworkUrls.length > 0) {
      message += `📎 ARTWORK FILES:\n`;
      artworkUrls.forEach((url, index) => {
        message += `${index + 1}. ${url}\n`;
      });
      message += `\n`;
    }

    // Payment details
    message += `💳 M-PESA PAYMENT DETAILS:\n`;
    message += `Business Number: 522533\n`;
    message += `Account Number: 7934479\n`;
    message += `Amount: KSh ${orderData.totalAmount.toLocaleString()}\n\n`;

    message += `I'm ready to proceed with this order and make the M-Pesa deposit of KSh ${orderData.totalAmount.toLocaleString()}. Please confirm and provide the project timeline. Thank you!`;

    return message;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 rounded w-1/2" />
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded" />
                <div className="h-4 bg-gray-200 rounded" />
                <div className="h-4 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
          <button
            onClick={() => router.back()}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center text-sm text-gray-600 hover:text-orange-600 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Product
          </button>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-3">Complete Your Order</h1>
            <p className="text-gray-600 max-w-xl mx-auto">
              Please provide the following information to complete your order. We'll get back to you shortly.
            </p>
          </div>

          <div className="grid grid-cols-12 gap-8">
            {/* Main Form Content */}
            <div className="col-span-12 lg:col-span-8 space-y-6">
              {/* Product Summary */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-start gap-4">
                  {product.imageUrl ? (
                    <div className="w-20 h-20 relative rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={product.imageUrl}
                        alt={product.service}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <PhotoIcon className="w-8 h-8 text-gray-400" />
                    </div>
                  )}
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{product.service}</h2>
                    <p className="text-gray-600 mt-1 text-sm">{product.description}</p>
                    <div className="mt-2 flex items-center text-sm">
                      <span className="font-medium text-gray-900">KSh {product.price?.toLocaleString()}</span>
                      <span className="mx-2 text-gray-400">•</span>
                      <span className="text-gray-600">{product.pricingType === 'per_meter' ? 'per meter' : 'per unit'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Service Type Selection */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Type</h3>
                <div className="grid grid-cols-2 gap-4">
                  <label 
                    className={`relative flex flex-col items-center bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-200 ${
                      orderForm.orderType === 'print' 
                        ? 'border-orange-500 bg-orange-50' 
                        : 'border-gray-200 hover:border-orange-200'
                    }`}
                  >
                    <input
                      type="radio"
                      name="orderType"
                      checked={orderForm.orderType === 'print'}
                      onChange={() => orderForm.setOrderType('print')}
                      className="sr-only"
                    />
                    <DocumentIcon className={`h-10 w-10 mb-3 ${
                      orderForm.orderType === 'print' ? 'text-orange-500' : 'text-gray-400'
                    }`} />
                    <span className="text-base font-medium text-gray-900">Print Only</span>
                    <span className="text-sm text-gray-500 text-center mt-1">Upload ready artwork</span>
                  </label>
                  <label 
                    className={`relative flex flex-col items-center bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-200 ${
                      orderForm.orderType === 'both' 
                        ? 'border-orange-500 bg-orange-50' 
                        : 'border-gray-200 hover:border-orange-200'
                    }`}
                  >
                    <input
                      type="radio"
                      name="orderType"
                      checked={orderForm.orderType === 'both'}
                      onChange={() => orderForm.setOrderType('both')}
                      className="sr-only"
                    />
                    <PhotoIcon className={`h-10 w-10 mb-3 ${
                      orderForm.orderType === 'both' ? 'text-orange-500' : 'text-gray-400'
                    }`} />
                    <span className="text-base font-medium text-gray-900">Design + Print</span>
                    <span className="text-sm text-gray-500 text-center mt-1">We design for you</span>
                  </label>
                </div>
              </div>

              {/* Customer Information */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      required
                      value={orderForm.customerInfo.name}
                      onChange={(e) => orderForm.setCustomerInfo({
                        ...orderForm.customerInfo,
                        name: e.target.value
                      })}
                      className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="tel"
                      required
                      value={orderForm.customerInfo.phone}
                      onChange={(e) => orderForm.setCustomerInfo({
                        ...orderForm.customerInfo,
                        phone: e.target.value
                      })}
                      className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                      placeholder="07XXXXXXXX"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      required
                      value={orderForm.customerInfo.email}
                      onChange={(e) => orderForm.setCustomerInfo({
                        ...orderForm.customerInfo,
                        email: e.target.value
                      })}
                      className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>

              {/* Design Requirements - Show if design is needed */}
              {orderForm.orderType === 'both' && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Design Requirements</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Design Brief <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        required
                        value={orderForm.designBrief}
                        onChange={(e) => orderForm.setDesignBrief(e.target.value)}
                        className="w-full border border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="Describe what you want designed... Include colors, style preferences, and any specific requirements."
                        rows={4}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Reference Files</label>
                      <div className="bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 p-6 hover:border-orange-300 transition-colors">
                        <ArtworkUpload
                          onFilesUploaded={(files) => {
                            orderForm.setUploadedArtwork([...orderForm.uploadedArtwork, ...files]);
                          }}
                          maxFiles={5}
                          disabled={false}
                        />
                        <p className="mt-2 text-sm text-gray-500">
                          Upload logos, brand assets, or inspiration images that will help us understand your requirements better.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Artwork Upload - Show for print only or both */}
              {(orderForm.orderType === 'print' || orderForm.orderType === 'both') && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {orderForm.orderType === 'both' ? 'Additional Artwork' : 'Print-Ready Artwork'}
                  </h3>
                  <div className="bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 p-6 hover:border-orange-300 transition-colors">
                    <ArtworkUpload
                      onFilesUploaded={(files) => {
                        orderForm.setUploadedArtwork([...orderForm.uploadedArtwork, ...files]);
                      }}
                      maxFiles={5}
                      disabled={false}
                    />
                    <p className="mt-2 text-sm text-gray-500">
                      {orderForm.orderType === 'both'
                        ? 'Upload any additional artwork or files that should be incorporated into the design.'
                        : 'Upload your print-ready artwork files. Accepted formats: AI, PSD, PDF, JPG, PNG.'}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Order Summary Sidebar */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Service Type</span>
                    <span className="font-medium text-gray-900">
                      {orderForm.orderType === 'print' ? 'Print Only' : 'Design + Print'}
                    </span>
                  </div>
                  {product.pricingType === 'per_meter' ? (
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Size (meters)</span>
                      <span className="font-medium text-gray-900">{orderForm.meters}</span>
                    </div>
                  ) : (
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Quantity</span>
                      <span className="font-medium text-gray-900">{orderForm.quantity}</span>
                    </div>
                  )}
                  {orderForm.orderType === 'both' && (
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Design Fee</span>
                      <span className="font-medium text-gray-900">
                        KSh {getDesignFee(product).toLocaleString()}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Production Cost</span>
                    <span className="font-medium text-gray-900">
                      KSh {getProductionCost(product, orderForm.quantity, orderForm.meters).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between py-3 text-lg">
                    <span className="font-semibold text-gray-900">Total</span>
                    <span className="font-bold text-orange-600">
                      KSh {(
                        getDesignFee(product) +
                        getProductionCost(product, orderForm.quantity, orderForm.meters)
                      ).toLocaleString()}
                    </span>
                  </div>

                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className={`w-full mt-6 py-3 px-4 rounded-lg text-white font-medium transition-all duration-200 ${
                      isSubmitting
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-orange-600 hover:bg-orange-700 shadow-lg hover:shadow-xl'
                    }`}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                    ) : 'Submit Order'}
                  </button>

                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500">
                      By submitting, you'll be redirected to WhatsApp to complete your order
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {orderForm.notification && (
        <div 
          className={`fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${
            orderForm.notification.type === 'success' 
              ? 'bg-green-500 text-white' 
              : 'bg-red-500 text-white'
          }`}
        >
          {orderForm.notification.message}
        </div>
      )}
    </div>
  );
} 