'use client';

import PageHero from '@/components/PageHero';
import { motion } from 'framer-motion';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import WebDevelopmentPortfolio from '@/components/WebDevelopmentPortfolio';

// Types for our technologies section
type TechStack = {
  id: string;
  category: string;
  icon: string;
  description: string;
  items: {
    name: string;
    icon: string;
    level: 'Basic' | 'Advanced' | 'Expert';
    experience: string;
  }[];
};

// Technologies data with updated styling
const technologies: TechStack[] = [
  {
    id: 'frontend',
    category: 'Frontend',
    icon: 'fas fa-laptop-code',
    description: 'Modern and responsive user interfaces',
    items: [
      {
        name: 'React/Next.js',
        icon: 'fab fa-react',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'TypeScript',
        icon: 'fas fa-code',
        level: 'Expert',
        experience: '4+ years'
      },
      {
        name: 'Tailwind CSS',
        icon: 'fas fa-paint-brush',
        level: 'Expert',
        experience: '3+ years'
      },
      {
        name: 'Vue.js',
        icon: 'fab fa-vuejs',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  },
  {
    id: 'backend',
    category: 'Backend',
    icon: 'fas fa-server',
    description: 'Scalable and secure server solutions',
    items: [
      {
        name: 'Node.js',
        icon: 'fab fa-node-js',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'Python',
        icon: 'fab fa-python',
        level: 'Advanced',
        experience: '4+ years'
      },
      {
        name: 'PHP/Laravel',
        icon: 'fab fa-php',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'Express',
        icon: 'fas fa-server',
        level: 'Expert',
        experience: '4+ years'
      }
    ]
  },
  {
    id: 'database',
    category: 'Database',
    icon: 'fas fa-database',
    description: 'Reliable data storage solutions',
    items: [
      {
        name: 'MySQL',
        icon: 'fas fa-database',
        level: 'Expert',
        experience: '6+ years'
      },
      {
        name: 'PostgreSQL',
        icon: 'fas fa-database',
        level: 'Advanced',
        experience: '4+ years'
      },
      {
        name: 'MongoDB',
        icon: 'fas fa-leaf',
        level: 'Expert',
        experience: '4+ years'
      },
      {
        name: 'Redis',
        icon: 'fas fa-bolt',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  },
  {
    id: 'cms',
    category: 'CMS',
    icon: 'fas fa-file-code',
    description: 'Content management solutions',
    items: [
      {
        name: 'WordPress',
        icon: 'fab fa-wordpress',
        level: 'Expert',
        experience: '7+ years'
      },
      {
        name: 'Strapi',
        icon: 'fas fa-box',
        level: 'Advanced',
        experience: '3+ years'
      },
      {
        name: 'Sanity',
        icon: 'fas fa-cube',
        level: 'Advanced',
        experience: '2+ years'
      },
      {
        name: 'Contentful',
        icon: 'fas fa-feather',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  }
];

export default function WebDevelopment() {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [showRequestForm, setShowRequestForm] = useState<boolean>(false);
  const [activeFaq, setActiveFaq] = useState<number | null>(null);
  const [mounted, setMounted] = useState<boolean>(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-[#0A1929]">
        <div className="container relative mx-auto px-6 pt-28 md:pt-16 pb-16 flex items-center min-h-[90vh]">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="space-y-6 md:space-y-8 mt-10 md:mt-0">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/5 border border-white/10">
                <span className="text-sm font-medium text-white">Web Development Services</span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                <span className="block mb-2">Modern Web</span>
                <span className="block text-[#FF5400]">Solutions</span>
              </h1>

              <p className="max-w-xl text-lg text-white/90 leading-relaxed">
                Professional websites that drive results. From simple landing pages to complex web applications, we build digital experiences that grow your business.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-5 pt-4">
                <a href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20web%20development%20services." 
                   target="_blank" rel="noopener noreferrer"
                   className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-8 py-4 rounded-full font-medium text-center transition-colors">
                  Start Your Project
                </a>
                <Link href="#portfolio"
                      className="border border-white/20 hover:bg-white/5 text-white px-8 py-4 rounded-full font-medium text-center transition-colors">
                  View Portfolio
                </Link>
              </div>
            </div>

            {/* Image */}
            <div className="relative h-[350px] md:h-[450px] mx-auto w-full max-w-lg">
              <div className="absolute inset-0 rounded-2xl overflow-hidden flex items-center justify-center">
                <Image
                  src="/images/web-01.svg"
                  alt="Web Development Services"
                  width={450}
                  height={450}
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-[#081422] to-transparent"></div>
      </section>

      {/* Services Overview */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#FF5400]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#0A1929]/5 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6"
            >
              <i className="fas fa-code text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Our Expertise</span>
            </motion.div>
            
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-6"
            >
              What We <span className="text-[#FF5400]">Build</span>
            </motion.h2>
            
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
            >
              From concept to launch, we create digital solutions that make an impact. 
              Our expertise spans across various web technologies to bring your vision to life.
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {[
              {
                icon: 'fas fa-globe',
                title: 'Business Websites',
                description: 'Professional websites that establish your online presence and build credibility with your audience.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-blue-50',
                features: ['Responsive Design', 'SEO Optimized', 'Fast Loading']
              },
              {
                icon: 'fas fa-shopping-cart',
                title: 'E-commerce Stores',
                description: 'Complete online stores with payment integration, inventory management, and order processing.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-green-50',
                features: ['Payment Gateway', 'Inventory System', 'Order Management']
              },
              {
                icon: 'fas fa-mobile-alt',
                title: 'Web Applications',
                description: 'Custom web applications tailored to your business processes and user requirements.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-purple-50',
                features: ['Custom Features', 'User Authentication', 'Real-time Updates']
              },
              {
                icon: 'fas fa-chart-line',
                title: 'Landing Pages',
                description: 'High-converting landing pages designed to capture leads and drive specific actions.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-orange-50',
                features: ['Lead Capture', 'A/B Testing', 'Conversion Tracking']
              },
              {
                icon: 'fas fa-blog',
                title: 'Content Management',
                description: 'Easy-to-manage websites with powerful CMS solutions for content updates.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-indigo-50',
                features: ['Easy Updates', 'Media Library', 'User Roles']
              },
              {
                icon: 'fas fa-rocket',
                title: 'Performance Optimization',
                description: 'Fast-loading, SEO-optimized websites that rank well and provide excellent user experience.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-red-50',
                features: ['Speed Optimization', 'SEO Ready', 'Analytics']
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group relative"
              >
                <div className="bg-white rounded-3xl p-8 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden">
                  {/* Gradient background on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF5400]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                  
                  {/* Icon with gradient background */}
                  <div className="relative mb-6">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <i className={`${service.icon} text-white text-lg`}></i>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-[#FF5400] rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                  </div>
                  
                  <div className="relative">
                    <h3 className="text-xl font-bold text-[#0A1929] mb-4 group-hover:text-[#FF5400] transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {service.description}
                    </p>
                    
                    {/* Feature tags */}
                    <div className="flex flex-wrap gap-2">
                      {service.features.map((feature, idx) => (
                        <span 
                          key={idx}
                          className="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full group-hover:bg-[#FF5400]/10 group-hover:text-[#FF5400] transition-all duration-300"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {/* Hover arrow */}
                  <div className="absolute bottom-6 right-6 w-8 h-8 bg-[#FF5400] rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transform translate-x-4 group-hover:translate-x-0 transition-all duration-300">
                    <i className="fas fa-arrow-right text-white text-sm"></i>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Bottom CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            <div className="bg-white rounded-2xl p-4 md:p-6 shadow-lg border border-gray-100 max-w-2xl mx-auto">
              {/* Mobile Layout */}
              <div className="flex flex-col sm:hidden gap-4">
                <div className="flex items-center justify-center gap-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                      <i className="fas fa-code text-white text-xs"></i>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-br from-[#0A1929] to-[#1a2332] rounded-full flex items-center justify-center">
                      <i className="fas fa-palette text-white text-xs"></i>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                      <i className="fas fa-rocket text-white text-xs"></i>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-[#0A1929]">Ready to start your project?</p>
                    <p className="text-xs text-gray-600">Let's discuss your requirements</p>
                  </div>
                </div>
                <a
                  href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20discussing%20my%20web%20development%20project."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center justify-center gap-2 w-full"
                >
                  <i className="fas fa-paper-plane"></i>
                  Get Started
                </a>
              </div>

              {/* Desktop Layout */}
              <div className="hidden sm:flex items-center gap-4">
                <div className="flex -space-x-2">
                  <div className="w-10 h-10 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                    <i className="fas fa-code text-white text-sm"></i>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-[#0A1929] to-[#1a2332] rounded-full flex items-center justify-center">
                    <i className="fas fa-palette text-white text-sm"></i>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                    <i className="fas fa-rocket text-white text-sm"></i>
                  </div>
                </div>
                <div className="text-left flex-1">
                  <p className="text-sm font-medium text-[#0A1929]">Ready to start your project?</p>
                  <p className="text-xs text-gray-600">Let's discuss your requirements</p>
                </div>
                <a
                  href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20discussing%20my%20web%20development%20project."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center gap-2 flex-shrink-0"
                >
                  <i className="fas fa-paper-plane"></i>
                  Get Started
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-4">
              Transparent Pricing
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-4">
              Choose the perfect package for your business needs
            </p>
            <div className="bg-orange-50 border border-orange-200 rounded-xl p-4 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-2">
                <i className="fas fa-info-circle text-[#FF5400]"></i>
                <span className="text-sm font-semibold text-[#FF5400]">Important Note</span>
              </div>
              <p className="text-sm text-gray-700 leading-relaxed">
                The prices shown are <strong>estimated ranges</strong> and not fixed prices. Each project is unique and will be 
                <strong> individually quoted</strong> based on your specific requirements, complexity, and additional features needed. 
                Contact us for a personalized quote tailored to your project.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Basic Website",
                price: "20,000",
                priceRange: "35,000",
                features: [
                  "Essential Pages (Home, About, Services, Contact)",
                  "Domain Registration (.com/.co.ke)",
                  "1 Year Web Hosting",
                  "SSL Certificate",
                  "Mobile Responsive Design",
                  "Basic SEO Setup"
                ]
              },
              {
                title: "Business Website",
                price: "35,000",
                priceRange: "75,000",
                features: [
                  "Up to 8 Custom Pages",
                  "Premium Domain & Hosting (1 Year)",
                  "Advanced Functionalities",
                  "Blog Integration",
                  "Advanced SEO Optimization",
                  "Social Media Integration"
                ],
                popular: true
              },
              {
                title: "E-commerce",
                price: "75,000",
                priceRange: "150,000",
                features: [
                  "Full E-commerce Website",
                  "Premium Domain & Hosting (1 Year)",
                  "Product Catalog (Up to 100 Products)",
                  "Payment Gateway Integration",
                  "Order Management System",
                  "Inventory Management"
                ]
              },
              {
                title: "Premium Custom",
                price: "150,000",
                priceRange: "+",
                features: [
                  "Custom Design & Features",
                  "Premium Domain & Hosting (1 Year)",
                  "Advanced Integrations",
                  "Custom Plugins Development",
                  "Unlimited Pages & Products",
                  "Priority Support"
                ]
              }
            ].map((plan, index) => {
              const whatsappMessage = `Hello! I'm interested in the ${plan.title} package (KSH ${plan.price}${plan.priceRange ? ` - ${plan.priceRange}` : ''}).

Features included:
${plan.features.map(feature => `✓ ${feature}`).join('\n')}

Please provide more information about this package.`;

              const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`bg-white rounded-2xl border-2 ${
                    plan.popular 
                      ? 'border-[#FF5400] shadow-lg shadow-[#FF5400]/10' 
                      : 'border-gray-100 hover:border-[#FF5400]/30'
                  } hover:shadow-xl transition-all duration-300 overflow-hidden relative group`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 right-0">
                      <div className="bg-[#FF5400] text-white text-xs font-medium px-4 py-2 rounded-bl-xl">
                        Most Popular
                      </div>
                    </div>
                  )}
                  <div className="p-8">
                    <h3 className="text-xl font-bold text-[#0A1929] mb-4">{plan.title}</h3>
                    <div className="mb-6">
                      <div className="flex items-baseline gap-1">
                        <span className="text-sm font-medium text-gray-500">KSH</span>
                        <span className="text-3xl font-bold text-[#FF5400]">{plan.price}</span>
                        {plan.priceRange && (
                          <>
                            <span className="text-lg font-medium text-gray-400 mx-1">-</span>
                            <span className="text-xl font-bold text-[#FF5400]">{plan.priceRange}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <ul className="space-y-3 mb-8 min-h-[200px]">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-3">
                          <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <i className="fas fa-check text-green-600 text-xs"></i>
                          </div>
                          <span className="text-gray-600 text-sm leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <a
                      href={whatsappUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-full py-4 px-6 rounded-xl text-sm font-medium transition-all duration-300 inline-flex items-center justify-center ${
                        plan.popular
                          ? 'bg-[#FF5400] text-white hover:bg-[#FF5400]/90 shadow-lg shadow-[#FF5400]/25'
                          : 'border-2 border-[#0A1929]/10 text-[#0A1929] hover:border-[#FF5400] hover:text-[#FF5400]'
                      }`}
                    >
                      Get Started
                      <i className="fas fa-arrow-right ml-2"></i>
                    </a>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Domain Names & Web Hosting Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-4">
              Domain Names & Web Hosting
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Understanding the foundation of your online presence - domain names and hosting services
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* Domain Names */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-blue-50 rounded-2xl p-8"
            >
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                  <i className="fas fa-globe text-white text-xl"></i>
                </div>
                <h3 className="text-2xl font-bold text-[#0A1929]">Domain Names</h3>
              </div>
              
              <p className="text-gray-700 mb-6 leading-relaxed">
                A domain name is your website's address on the internet - like <strong>mocky.co.ke</strong>. 
                It's what people type into their browser to find your website. Think of it as your digital real estate address.
              </p>
              
              <ul className="space-y-3">
                {[
                  'Provides a memorable way for people to find your website',
                  'Builds brand recognition and credibility',
                  'Available in various extensions (.com, .co.ke, .org, etc.)'
                ].map((item, idx) => (
                  <li key={idx} className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <i className="fas fa-check text-white text-xs"></i>
                    </div>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Web Hosting */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-green-50 rounded-2xl p-8"
            >
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                  <i className="fas fa-server text-white text-xl"></i>
                </div>
                <h3 className="text-2xl font-bold text-[#0A1929]">Web Hosting</h3>
              </div>
              
              <p className="text-gray-700 mb-6 leading-relaxed">
                Web hosting is the service that stores your website files and makes them accessible on the internet. 
                It's like renting space on a powerful computer (server) that's connected to the internet 24/7.
              </p>
              
              <ul className="space-y-3">
                {[
                  'Stores your website files, images, and databases',
                  'Ensures your website is accessible 24/7',
                  'Provides email services and technical support'
                ].map((item, idx) => (
                  <li key={idx} className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <i className="fas fa-check text-white text-xs"></i>
                    </div>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <a
              href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I%20need%20help%20with%20domain%20registration%20and%20web%20hosting."
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-3 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-8 py-4 rounded-full font-medium transition-colors"
            >
              <i className="fas fa-paper-plane text-xl"></i>
              Get Domain & Hosting Help
            </a>
          </div>
        </div>
      </section>

      {/* VPS & Cloud Configurations Section */}
      <section className="py-20 bg-gradient-to-br from-[#0A1929] to-[#1a2332] relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#FF5400]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 w-64 h-64 bg-[#FF5400]/5 rounded-full blur-2xl"></div>
        
        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 border border-white/20 backdrop-blur-sm mb-6"
            >
              <i className="fas fa-server text-[#FF5400]"></i>
              <span className="text-sm font-medium text-white">Server Solutions</span>
            </motion.div>
            
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl font-bold text-white mb-6"
            >
              VPS Server Setups & <span className="text-[#FF5400]">Cloud Configurations</span>
            </motion.h2>
            
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed"
            >
              Professional server management and cloud infrastructure setup for scalable, 
              secure, and high-performance web applications.
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                icon: 'fas fa-server',
                title: 'VPS Server Setup',
                description: 'Complete virtual private server configuration with optimized performance and security settings.',
                features: ['Ubuntu/CentOS Installation', 'Security Hardening', 'Performance Optimization'],
                color: 'from-[#FF5400] to-[#FF7A00]'
              },
              {
                icon: 'fas fa-cloud',
                title: 'Cloud Infrastructure',
                description: 'Scalable cloud solutions on AWS, Google Cloud, and DigitalOcean platforms.',
                features: ['Auto-scaling Setup', 'Load Balancing', 'CDN Configuration'],
                color: 'from-blue-500 to-blue-600'
              },
              {
                icon: 'fas fa-database',
                title: 'Database Management',
                description: 'Professional database setup, optimization, and backup strategies for your applications.',
                features: ['MySQL/PostgreSQL Setup', 'Database Optimization', 'Automated Backups'],
                color: 'from-green-500 to-green-600'
              },
              {
                icon: 'fas fa-shield-alt',
                title: 'Security Configuration',
                description: 'Comprehensive security measures including SSL, firewalls, and monitoring systems.',
                features: ['SSL Certificate Setup', 'Firewall Configuration', 'Security Monitoring'],
                color: 'from-red-500 to-red-600'
              },
              {
                icon: 'fas fa-rocket',
                title: 'Performance Optimization',
                description: 'Server and application optimization for maximum speed and efficiency.',
                features: ['Caching Solutions', 'Resource Optimization', 'Speed Enhancement'],
                color: 'from-purple-500 to-purple-600'
              },
              {
                icon: 'fas fa-tools',
                title: 'DevOps Solutions',
                description: 'Complete CI/CD pipeline setup and deployment automation for streamlined development.',
                features: ['CI/CD Pipeline', 'Docker Containers', 'Automated Deployment'],
                color: 'from-indigo-500 to-indigo-600'
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group relative"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-[#FF5400]/50 hover:bg-white/15 transition-all duration-500 h-full relative overflow-hidden">
                  {/* Hover gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF5400]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
                  
                  {/* Icon */}
                  <div className="relative mb-6">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <i className={`${service.icon} text-white text-lg`}></i>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-[#FF5400] rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                  </div>

                  {/* Content */}
                  <div className="relative">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-[#FF5400] transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-white/80 text-sm mb-6 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Features */}
                    <div className="space-y-3">
                      {service.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-3">
                          <div className="w-4 h-4 bg-[#FF5400] rounded-full flex items-center justify-center flex-shrink-0">
                            <i className="fas fa-check text-white text-xs"></i>
                          </div>
                          <span className="text-white/90 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Hover arrow */}
                    <div className="absolute bottom-0 right-0 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                      <i className="fas fa-arrow-right text-[#FF5400] text-lg"></i>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Bottom CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            {/* Mobile layout */}
            <div className="sm:hidden">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 max-w-sm mx-auto">
                <div className="flex flex-col gap-4">
                  <div className="flex justify-center -space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                      <i className="fas fa-server text-white text-xs"></i>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                      <i className="fas fa-cloud text-white text-xs"></i>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                      <i className="fas fa-database text-white text-xs"></i>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-white/90 text-sm font-medium mb-1">Need Server Setup?</p>
                    <p className="text-white/70 text-xs">Professional VPS & cloud configuration</p>
                  </div>
                  <a
                    href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20VPS%20server%20setup%20and%20cloud%20configuration%20services."
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center justify-center gap-2 w-full"
                  >
                    <i className="fas fa-paper-plane"></i>
                    Get Server Help
                  </a>
                </div>
              </div>
            </div>

            {/* Desktop layout */}
            <div className="hidden sm:flex">
              <div className="inline-flex items-center gap-4 bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="flex -space-x-2">
                  <div className="w-10 h-10 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                    <i className="fas fa-server text-white text-sm"></i>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <i className="fas fa-cloud text-white text-sm"></i>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                    <i className="fas fa-database text-white text-sm"></i>
                  </div>
                </div>
                <div className="text-left">
                  <p className="text-white font-medium">Need Professional Server Setup?</p>
                  <p className="text-white/70 text-sm">VPS configuration, cloud infrastructure & DevOps solutions</p>
                </div>
                <a
                  href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20VPS%20server%20setup%20and%20cloud%20configuration%20services."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center gap-2 flex-shrink-0"
                >
                  <i className="fas fa-paper-plane"></i>
                  Get Server Help
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#0A1929]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#FF5400]/5 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#0A1929]/10 border border-[#0A1929]/20 mb-6"
            >
              <i className="fas fa-cogs text-[#0A1929]"></i>
              <span className="text-sm font-medium text-[#0A1929]">Tech Stack</span>
            </motion.div>
            
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-6"
            >
              Technologies We <span className="text-[#FF5400]">Use</span>
            </motion.h2>
            
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
            >
              Modern tools and frameworks to build robust, scalable web solutions. 
              Our expertise spans across the entire development stack.
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {technologies.map((tech, index) => (
              <motion.div
                key={tech.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.15 }}
                viewport={{ once: true }}
                className="group relative"
              >
                <div className="bg-white rounded-3xl p-8 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden">
                  {/* Gradient background on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#0A1929]/5 to-[#FF5400]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                  
                  {/* Header with icon */}
                  <div className="relative mb-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i className={`${tech.icon} text-white text-2xl`}></i>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-[#0A1929] group-hover:text-[#FF5400] transition-colors duration-300">
                          {tech.category}
                        </h3>
                        <div className="w-12 h-1 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] rounded-full mt-2 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {tech.description}
                    </p>
                  </div>
                  
                  {/* Technologies list */}
                  <div className="relative space-y-4">
                    {tech.items.map((item, idx) => (
                      <motion.div 
                        key={idx}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: (index * 0.15) + (idx * 0.1) }}
                        viewport={{ once: true }}
                        className="flex items-center justify-between p-3 rounded-xl bg-gray-50 group-hover:bg-white transition-colors duration-300 border border-transparent group-hover:border-[#FF5400]/20"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-300">
                            <i className={`${item.icon} text-[#FF5400] text-sm`}></i>
                          </div>
                          <span className="text-sm font-medium text-gray-800 group-hover:text-[#0A1929] transition-colors duration-300">
                            {item.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`text-xs px-3 py-1.5 rounded-full font-medium transition-all duration-300 ${
                            item.level === 'Expert' 
                              ? 'bg-green-100 text-green-700 group-hover:bg-green-500 group-hover:text-white'
                              : item.level === 'Advanced'
                              ? 'bg-blue-100 text-blue-700 group-hover:bg-blue-500 group-hover:text-white'
                              : 'bg-gray-100 text-gray-700 group-hover:bg-gray-500 group-hover:text-white'
                          }`}>
                            {item.level}
                          </span>
                          {item.level === 'Expert' && (
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Experience indicator */}
                  <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="w-8 h-8 bg-[#FF5400] rounded-full flex items-center justify-center">
                      <i className="fas fa-star text-white text-sm"></i>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>


        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 bg-white">
        <WebDevelopmentPortfolio />
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Common questions about our web development services
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-6">
              {[
                {
                  question: "How long does it take to build a website?",
                  answer: "Timeline varies based on complexity. A basic website takes 1-2 weeks, business websites 2-4 weeks, and e-commerce sites 4-8 weeks. We'll provide a detailed timeline during consultation."
                },
                {
                  question: "What's the difference between shared hosting and VPS?",
                  answer: "Shared hosting means your website shares server resources with other sites, while VPS (Virtual Private Server) gives you dedicated resources and better performance. VPS is ideal for high-traffic sites, e-commerce stores, or applications requiring specific server configurations."
                },
                {
                  question: "Do you provide VPS server setup and management?",
                  answer: "Yes, we offer complete VPS server setup including Ubuntu/CentOS installation, security hardening, performance optimization, and ongoing server management. We handle everything from initial configuration to regular maintenance and monitoring."
                },
                {
                  question: "Which cloud platforms do you work with?",
                  answer: "We work with major cloud providers including AWS, Google Cloud Platform, DigitalOcean, and Linode. We'll recommend the best platform based on your specific needs, budget, and technical requirements."
                },
                {
                  question: "Do you provide ongoing maintenance?",
                  answer: "Yes, we offer maintenance packages including security updates, content updates, backups, server monitoring, and 24/7 technical support to keep your website and server running smoothly."
                },
                {
                  question: "Will my website be mobile-friendly?",
                  answer: "Absolutely! All our websites are built with responsive design, ensuring they look and work perfectly on all devices - desktop, tablet, and mobile."
                },
                {
                  question: "Can you help with database optimization and management?",
                  answer: "Yes, we provide comprehensive database services including MySQL/PostgreSQL setup, performance optimization, automated backups, and database security configuration. We ensure your data is secure and your queries run efficiently."
                },
                {
                  question: "Do you offer DevOps and CI/CD pipeline setup?",
                  answer: "Absolutely! We set up complete CI/CD pipelines using tools like GitHub Actions, Docker containers, and automated deployment systems. This streamlines your development process and ensures reliable, consistent deployments."
                },
                {
                  question: "What security measures do you implement?",
                  answer: "We implement comprehensive security including SSL certificates, firewall configuration, regular security updates, malware scanning, DDoS protection, and continuous monitoring. For VPS setups, we also include server hardening and intrusion detection."
                }
              ].map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                  viewport={{ once: true }}
                  className="mb-4"
                >
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full bg-white rounded-xl p-6 text-left border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-lg transition-all duration-300 h-full"
                  >
                    <div className="flex items-start justify-between">
                      <h3 className="text-base font-semibold text-[#0A1929] pr-4 leading-tight">{faq.question}</h3>
                      <i className={`fas fa-chevron-${activeFaq === index ? 'up' : 'down'} text-[#FF5400] transition-transform duration-300 flex-shrink-0 mt-1`}></i>
                    </div>
                    {activeFaq === index && (
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed text-sm">{faq.answer}</p>
                      </div>
                    )}
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>


    </div>
  );
}