import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { sendEmailWithAttachments } from '@/services/emailService';
import { z } from 'zod';

const SendEmailSchema = z.object({
  to: z.string().email('Invalid email address'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  attachments: z.array(z.object({
    fileName: z.string(),
    originalName: z.string(),
    fileUrl: z.union([z.string().min(1), z.any()]).transform((val) => {
      // Convert any value to string for S3 URLs
      return typeof val === 'string' ? val : String(val);
    }),
    fileType: z.string(),
  })).optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Send Email API] POST request from user: ${user.email}`);
    
    // Parse request body
    const data = await request.json();
    console.log('[Send Email API] Request data:', JSON.stringify(data, null, 2));
    
    // Validate the data
    const validationResult = SendEmailSchema.safeParse(data);
    if (!validationResult.success) {
      console.warn('[Send Email API] Invalid data:', validationResult.error.format());
      return NextResponse.json(
        { error: 'Invalid data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { to, subject, message, attachments } = validationResult.data;

    // Prepare email content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%); color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Mocky Digital</h1>
          <p style="margin: 5px 0 0 0; opacity: 0.9;">Your Design is Ready!</p>
        </div>
        
        <div style="padding: 30px 20px; background: #ffffff;">
          <div style="white-space: pre-line; line-height: 1.6; color: #374151;">
            ${message}
          </div>
          
          ${attachments && attachments.length > 0 ? `
            <div style="margin-top: 30px; padding: 20px; background: #f9fafb; border-radius: 8px; border-left: 4px solid #1e40af;">
              <h3 style="margin: 0 0 15px 0; color: #1e40af; font-size: 16px;">📎 Attached Files</h3>
              <ul style="margin: 0; padding-left: 20px; color: #6b7280;">
                ${attachments.map(att => `<li>${att.originalName}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
        
        <div style="background: #f3f4f6; padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
          <p style="margin: 0;">
            <strong>Mocky Digital</strong><br>
            📧 <EMAIL> | 📱 +254 741 590 670<br>
            Professional Design & Printing Services
          </p>
        </div>
      </div>
    `;

    // Prepare attachments for email
    const emailAttachments = attachments?.map(att => ({
      filename: att.originalName,
      path: att.fileUrl, // Nodemailer can handle URLs directly
      contentType: att.fileType,
    })) || [];

    // Send email
    const emailResult = await sendEmailWithAttachments({
      to,
      subject,
      html: emailHtml,
      text: message,
      attachments: emailAttachments,
      from: '<EMAIL>',
    });

    if (!emailResult.success) {
      return NextResponse.json(
        { error: 'Failed to send email', details: emailResult.error },
        { status: 500 }
      );
    }

    console.log(`[Send Email API] Email sent successfully to ${to} by user: ${user.email}`);

    return NextResponse.json({
      success: true,
      message: 'Email sent successfully',
      messageId: emailResult.messageId,
    });

  } catch (error) {
    console.error('Error in send email API:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
} 