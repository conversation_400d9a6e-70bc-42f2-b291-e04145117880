import Image from 'next/image';
import Link from 'next/link';
import ClientCallbackForm from '@/components/ClientCallbackForm';
import PortfolioSlider from '@/components/PortfolioSlider';
import ServicesSlider from '@/components/ServicesSlider';
import { getSiteSettings } from '@/services/siteSettingsService';
import WebDevelopmentPortfolioSection from '@/components/WebDevelopmentPortfolioSection';
import Testimonials from '@/components/Testimonials';

// Export metadata directly in the page file
export const metadata = {
  title: 'Mocky Digital - Web Design & Digital Marketing Agency Kenya',
  description: 'Professional web design, graphic design, and digital marketing services in Kenya.',
  keywords: 'graphic design, web development, digital marketing, branding, Kenya, Nairobi'
};

// Server Component
export default async function Home() {
  // Get site settings
  const settings = await getSiteSettings();

  // WhatsApp link precomputed
  const whatsappLink = "https://wa.me/254741590670?text=Hello%20Mocky%20Graphics!";

  return (
    <main className="overflow-hidden">
      {/* Hero Section - Modified with better mobile padding */}
      <section className="relative overflow-hidden bg-[#0A1929]">
        <div className="container relative mx-auto px-6 pt-28 md:pt-16 pb-16 flex items-center min-h-[90vh]">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="space-y-6 md:space-y-8 mt-10 md:mt-0">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/5 border border-white/10">
                <span className="text-sm font-medium text-white">Welcome to Mocky Digital</span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                <span className="block mb-2">Transform Your</span>
                <span className="block text-blue-200">Digital Presence</span>
              </h1>

              <p className="max-w-xl text-lg text-white/90 leading-relaxed">
                We create effective visual experiences that drive growth
                through strategic branding and innovative digital solutions.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-5 pt-4">
                <a href={whatsappLink} target="_blank" rel="noopener noreferrer"
                  className="bg-white hover:bg-gray-100 text-[#0A1929] px-8 py-4 rounded-full font-medium text-center transition-colors">
                  Get Started
                </a>
                <Link href="/portfolio"
                  className="border border-white/20 hover:bg-white/5 text-white px-8 py-4 rounded-full font-medium text-center transition-colors">
                  View Our Work
                </Link>
              </div>
            </div>

            {/* Image */}
            <div className="relative h-[350px] md:h-[450px] mx-auto w-full max-w-lg">
              <div className="absolute inset-0 rounded-2xl overflow-hidden flex items-center justify-center">
                <Image
                  src="/images/hero/2.svg"
                  alt="Mocky Digital Hero"
                  width={450}
                  height={450}
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-[#081422] to-transparent"></div>
      </section>

      {/* Services Slider */}
      <ServicesSlider />

      {/* About Us & Callback Form Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* About Us Content - Left Side */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h2 className="text-3xl md:text-4xl font-bold text-[#FF5400]">
                  A Little bit about us
                </h2>
                
                <div className="space-y-6 text-gray-700 leading-relaxed">
                  <p className="text-lg">
                    We're a company of people who love helping people succeed. When organizations win, we take great pride in that achievement.
                  </p>
                  
                  <p className="text-lg">
                    Our Nairobi-based team blends strategy, creativity, and technology to deliver powerful results for organizations across the African market and beyond.
                  </p>
                </div>
              </div>


            </div>

            {/* Callback Form - Right Side */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="mb-8">
                <h3 className="text-3xl md:text-4xl font-bold text-[#FF5400] mb-4">
                  Request a Callback
                </h3>
                <p className="text-gray-600">
                  Fill out the form below and we'll get back to you shortly
                </p>
              </div>

              <ClientCallbackForm />
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview Section */}
      <section className="py-24 bg-white" id="services">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-[#FF5400]">
              Our Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Comprehensive creative and digital solutions to get your branding right
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* 01. Creative Design Services */}
            <div className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-[#FF5400]">01.</span>
                <h3 className="text-xl font-bold text-gray-900">Creative Design Services</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Professional creative design solutions that bring your vision to life
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Logo, Business Cards, & Letterheads</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Company Profiles, Catalogues, & Newsletters</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Flyers, Posters, & Publications</span>
                </li>
              </ul>
              <Link
                href="/services#creative-design"
                className="text-[#FF5400] hover:text-[#FF5400]/80 font-medium inline-flex items-center"
              >
                Read More →
              </Link>
            </div>

            {/* 02. Branding Services */}
            <div className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-[#FF5400]">02.</span>
                <h3 className="text-xl font-bold text-gray-900">Branding Services</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Complete branding solutions to establish your market presence
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Printing & Merchandise Branding</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Office & Shop Branding</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Signage & Wall Branding</span>
                </li>
              </ul>
              <Link
                href="/services#branding"
                className="text-[#FF5400] hover:text-[#FF5400]/80 font-medium inline-flex items-center"
              >
                Read More →
              </Link>
            </div>

            {/* 03. Advertising Services */}
            <div className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-[#FF5400]">03.</span>
                <h3 className="text-xl font-bold text-gray-900">Advertising Services</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Strategic advertising campaigns that drive results and engagement
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Campaign Strategy</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Media Buying & Planning</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Promotion & Activations Licensing</span>
                </li>
              </ul>
              <Link
                href="/services#advertising"
                className="text-[#FF5400] hover:text-[#FF5400]/80 font-medium inline-flex items-center"
              >
                Read More →
              </Link>
            </div>

            {/* 04. Digital Services */}
            <div className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-[#FF5400]">04.</span>
                <h3 className="text-xl font-bold text-gray-900">Digital Services</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Comprehensive digital marketing solutions to boost your online presence
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Social Media Marketing</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Search Engine Optimization</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Sponsored Ads</span>
                </li>
              </ul>
              <Link
                href="/services#digital"
                className="text-[#FF5400] hover:text-[#FF5400]/80 font-medium inline-flex items-center"
              >
                Read More →
              </Link>
            </div>

            {/* 05. IT Solutions */}
            <div className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-[#FF5400]">05.</span>
                <h3 className="text-xl font-bold text-gray-900">IT Solutions</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Comprehensive IT solutions to streamline your business operations
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Web Development</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Cloud Solutions</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">System Integration</span>
                </li>
              </ul>
              <Link
                href="/services#it-solutions"
                className="text-[#FF5400] hover:text-[#FF5400]/80 font-medium inline-flex items-center"
              >
                Read More →
              </Link>
            </div>

            {/* 06. Consultancy Services */}
            <div className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-[#FF5400]">06.</span>
                <h3 className="text-xl font-bold text-gray-900">Consultancy Services</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Expert business consultancy to guide your branding and marketing strategy
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Business Branding & Advertising</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Market Research & Communication</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="h-2 w-2 rounded-full bg-[#FF5400]"></span>
                  <span className="text-gray-600">Brand Awareness Training</span>
                </li>
              </ul>
              <Link
                href="/services#consultancy"
                className="text-[#FF5400] hover:text-[#FF5400]/80 font-medium inline-flex items-center"
              >
                Read More →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Slider Section */}
      <PortfolioSlider />

      {/* Web Development Portfolio Section */}
      <WebDevelopmentPortfolioSection />



      {/* How We Work Section */}
      <section className="py-24 bg-[#0A1929] relative overflow-hidden" id="process">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 border border-white/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-48 h-48 border border-white/20 rounded-full animate-pulse delay-300"></div>
          <div className="absolute top-1/2 left-1/4 w-32 h-32 border border-white/20 rounded-full animate-pulse delay-700"></div>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full text-white/80 text-sm font-medium mb-6">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Our Process
            </div>
                         <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
               How We <span className="bg-gradient-to-r from-[#FF5400] to-[#FF7A00] bg-clip-text text-transparent">Transform</span> Ideas
             </h2>
             <p className="text-lg text-white/70 max-w-3xl mx-auto leading-relaxed">
               From concept to completion, our streamlined 4-step process ensures exceptional results that exceed expectations
             </p>
          </div>

          {/* Process Timeline */}
          <div className="relative max-w-7xl mx-auto">
            {/* Animated Connection Line */}
            <div className="hidden lg:block absolute top-24 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white/20 to-transparent">
              <div className="absolute inset-0 bg-gradient-to-r from-[#FF5400] via-[#FF7A00] to-[#FF5400] opacity-60 animate-pulse"></div>
            </div>

                         {/* Process Steps */}
             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 items-stretch">
              {/* Step 1: Discovery & Planning */}
              <div className="relative group">
                {/* Connecting Line for Mobile */}
                <div className="lg:hidden absolute left-1/2 top-20 w-0.5 h-16 bg-gradient-to-b from-[#FF5400] to-transparent transform -translate-x-1/2"></div>
                
                                 <div className="relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#FF5400]/20 group-hover:border-[#FF5400]/30 h-full flex flex-col">
                  {/* Floating Number Badge */}
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#FF5400] to-[#FF7A00] flex items-center justify-center text-white text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                      01
                    </div>
                  </div>

                                     <div className="text-center pt-6 flex-1 flex flex-col">
                     {/* Icon Container */}
                     <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-[#FF5400]/20 to-[#FF7A00]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                       <svg className="w-10 h-10 text-[#FF5400]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                       </svg>
                     </div>

                     <h3 className="text-2xl font-bold text-white mb-4">Discovery & Planning</h3>
                     <p className="text-white/70 leading-relaxed mb-6 flex-1">
                       We dive deep into understanding your vision, goals, and requirements through detailed consultation
                     </p>
                     
                     {/* Features List */}
                     <div className="space-y-2 text-sm text-white/60 mt-auto">
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FF5400]"></div>
                         <span>Project briefing</span>
                       </div>
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FF5400]"></div>
                         <span>Strategy development</span>
                       </div>
                     </div>
                   </div>
                </div>
              </div>

              {/* Step 2: Design & Development */}
              <div className="relative group">
                <div className="lg:hidden absolute left-1/2 top-20 w-0.5 h-16 bg-gradient-to-b from-[#FF7A00] to-transparent transform -translate-x-1/2"></div>
                
                                 <div className="relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#FF7A00]/20 group-hover:border-[#FF7A00]/30 h-full flex flex-col">
                   <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                     <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#FF7A00] to-[#FFA500] flex items-center justify-center text-white text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                       02
                     </div>
                   </div>

                   <div className="text-center pt-6 flex-1 flex flex-col">
                     <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-[#FF7A00]/20 to-[#FFA500]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                       <svg className="w-10 h-10 text-[#FF7A00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                       </svg>
                     </div>

                     <h3 className="text-2xl font-bold text-white mb-4">Design & Development</h3>
                     <p className="text-white/70 leading-relaxed mb-6 flex-1">
                       Our creative team brings your vision to life with stunning designs and robust development
                     </p>
                     
                     <div className="space-y-2 text-sm text-white/60 mt-auto">
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FF7A00]"></div>
                         <span>Creative concepts</span>
                       </div>
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FF7A00]"></div>
                         <span>Technical implementation</span>
                       </div>
                     </div>
                   </div>
                 </div>
              </div>

              {/* Step 3: Review & Refinement */}
              <div className="relative group">
                <div className="lg:hidden absolute left-1/2 top-20 w-0.5 h-16 bg-gradient-to-b from-[#FFA500] to-transparent transform -translate-x-1/2"></div>
                
                                 <div className="relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#FFA500]/20 group-hover:border-[#FFA500]/30 h-full flex flex-col">
                   <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                     <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#FFA500] to-[#FFD700] flex items-center justify-center text-white text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                       03
                     </div>
                   </div>

                   <div className="text-center pt-6 flex-1 flex flex-col">
                     <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-[#FFA500]/20 to-[#FFD700]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                       <svg className="w-10 h-10 text-[#FFA500]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                       </svg>
                     </div>

                     <h3 className="text-2xl font-bold text-white mb-4">Review & Refinement</h3>
                     <p className="text-white/70 leading-relaxed mb-6 flex-1">
                       Collaborative feedback sessions ensure every detail meets your expectations and brand standards
                     </p>
                     
                     <div className="space-y-2 text-sm text-white/60 mt-auto">
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FFA500]"></div>
                         <span>Client feedback</span>
                       </div>
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FFA500]"></div>
                         <span>Quality assurance</span>
                       </div>
                     </div>
                   </div>
                 </div>
              </div>

              {/* Step 4: Launch & Support */}
              <div className="relative group">
                                 <div className="relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#FFD700]/20 group-hover:border-[#FFD700]/30 h-full flex flex-col">
                   <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                     <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#FFD700] to-[#FF5400] flex items-center justify-center text-white text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                       04
                     </div>
                   </div>

                   <div className="text-center pt-6 flex-1 flex flex-col">
                     <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-[#FFD700]/20 to-[#FF5400]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                       <svg className="w-10 h-10 text-[#FFD700]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                       </svg>
                     </div>

                     <h3 className="text-2xl font-bold text-white mb-4">Launch & Support</h3>
                     <p className="text-white/70 leading-relaxed mb-6 flex-1">
                       Seamless deployment and ongoing support to ensure your success in the digital landscape
                     </p>
                     
                     <div className="space-y-2 text-sm text-white/60 mt-auto">
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FFD700]"></div>
                         <span>Final delivery</span>
                       </div>
                       <div className="flex items-center justify-center gap-2">
                         <div className="w-1.5 h-1.5 rounded-full bg-[#FFD700]"></div>
                         <span>Ongoing support</span>
                       </div>
                     </div>
                   </div>
                 </div>
              </div>
            </div>

            
          </div>
        </div>
      </section>

      {/* Why Us Section */}
      <section className="py-24 bg-white" id="why-us">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-[#FF5400]">
              Why Choose Us
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We combine creativity, strategy, and technical expertise to deliver exceptional results
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {/* Advantage 1 */}
            <div className="bg-gray-50 p-8 rounded-xl text-center hover:shadow-lg transition-shadow relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FF5400] to-[#FF7A00]"></div>
              <div className="mb-6 inline-flex items-center justify-center w-16 h-16 rounded-full bg-white shadow-md text-[#FF5400]">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Fast Turnaround</h3>
              <p className="text-gray-600">
                We deliver high-quality results on time, ensuring your business stays ahead of the competition.
              </p>
              <div className="mt-6 text-3xl font-bold text-[#FF5400]">95%</div>
              <div className="text-sm text-gray-500">On-time delivery rate</div>
            </div>

            {/* Advantage 2 */}
            <div className="bg-gray-50 p-8 rounded-xl text-center hover:shadow-lg transition-shadow relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FF7A00] to-[#FFA500]"></div>
              <div className="mb-6 inline-flex items-center justify-center w-16 h-16 rounded-full bg-white shadow-md text-[#FF7A00]">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Dedicated Team</h3>
              <p className="text-gray-600">
                Our team of experts is committed to the success of your project from inception to completion.
              </p>
              <div className="mt-6 text-3xl font-bold text-[#FF7A00]">12+</div>
              <div className="text-sm text-gray-500">Industry specialists</div>
            </div>

            {/* Advantage 3 */}
            <div className="bg-gray-50 p-8 rounded-xl text-center hover:shadow-lg transition-shadow relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FFA500] to-[#FFD700]"></div>
              <div className="mb-6 inline-flex items-center justify-center w-16 h-16 rounded-full bg-white shadow-md text-[#FFA500]">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Quality Assurance</h3>
              <p className="text-gray-600">
                We maintain rigorous quality standards to ensure flawless execution of every project.
              </p>
              <div className="mt-6 text-3xl font-bold text-[#FFA500]">100%</div>
              <div className="text-sm text-gray-500">Satisfaction guarantee</div>
            </div>

            {/* Advantage 4 */}
            <div className="bg-gray-50 p-8 rounded-xl text-center hover:shadow-lg transition-shadow relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FFD700] to-[#4299E1]"></div>
              <div className="mb-6 inline-flex items-center justify-center w-16 h-16 rounded-full bg-white shadow-md text-[#4299E1]">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Innovative Solutions</h3>
              <p className="text-gray-600">
                We leverage the latest technologies and creative approaches to solve complex challenges.
              </p>
              <div className="mt-6 text-3xl font-bold text-[#4299E1]">250+</div>
              <div className="text-sm text-gray-500">Projects completed</div>
            </div>
          </div>

          {/* Testimonial Section */}
          <Testimonials />
        </div>
      </section>

      {/* Social Media Section - Using data from the database */}
      <section className="py-20 bg-gray-50" id="connect">
        <div className="container mx-auto px-6">
          <div className="max-w-xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
              Connect With Us
            </h2>
            <p className="text-lg text-gray-600 mb-10">
              Follow us on social media for updates and inspiration
            </p>

            <div className="flex flex-col gap-4 max-w-md mx-auto">
              {/* Facebook - Use database setting or fallback to default */}
              <a
                href={settings?.facebookUrl || "https://facebook.com/mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-[#1877F2] hover:bg-[#166FE5] text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                </svg>
                <span>Follow on Facebook</span>
              </a>

              {/* Instagram - Use database setting or fallback to default */}
              <a
                href={settings?.instagramUrl || "https://instagram.com/mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#F77737] hover:opacity-90 text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
                <span>Follow on Instagram</span>
              </a>

              {/* X (Twitter) - Use database setting or fallback to default */}
              <a
                href={settings?.twitterUrl || "https://twitter.com/mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black hover:bg-gray-900 text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z" />
                </svg>
                <span>Follow on X</span>
              </a>

              {/* TikTok - Use database setting or fallback to default */}
              <a
                href={settings?.tiktokUrl || "https://www.tiktok.com/@mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black hover:bg-gray-900 text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" className="h-5 w-5 fill-current">
                  <path d="M448 209.9a210.1 210.1 0 0 1 -122.8-39.3V349.4A162.6 162.6 0 1 1 185 188.3V278.2a74.6 74.6 0 1 0 52.2 71.2V0l88 0a121.2 121.2 0 0 0 1.9 22.2h0A122.2 122.2 0 0 0 381 102.4a121.4 121.4 0 0 0 67 20.1z" />
                </svg>
                <span>Follow on TikTok</span>
              </a>

              {/* LinkedIn - Only show if URL exists in settings */}
              {settings?.linkedinUrl && (
                <a
                  href={settings.linkedinUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#0077B5] hover:bg-[#006699] text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                  <span>Follow on LinkedIn</span>
                </a>
              )}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}


