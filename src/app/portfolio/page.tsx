import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { Suspense } from 'react';
import { ImageItem } from '@/utils/getImages';
import { readPortfolioMetadata } from '@/app/api/admin/portfolio/route';
import ClientGallerySection from '@/components/ClientGallerySection';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { unstable_cache } from 'next/cache';

// Enhanced caching configuration with ISR
export const revalidate = 300; // 5 minutes
export const dynamic = 'force-static';

// Cache version for busting cache when needed
const CACHE_VERSION = process.env.PORTFOLIO_CACHE_VERSION || 'v1';

// Function to shuffle array
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Enhanced cached function for portfolio data with versioning
const getCachedPortfolioByCategory = unstable_cache(
  async (category: string, version: string): Promise<ImageItem[]> => {
    try {
      const portfolioItems = await readPortfolioMetadata();
      
      // Filter for the specific category and exclude soft-deleted items
      const categoryItems = portfolioItems.filter(item => 
        item.category === category && !item.deletedAt
      );
      
      // Convert to ImageItem format
      const images: ImageItem[] = categoryItems?.map((item, index) => ({
        id: index + 1,
        url: item.imageSrc,
        src: item.imageSrc,
        alt: item.alt || item.title || `${category} image`,
        title: item.title || '',
        category: category,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      })) || [];
      
      console.log(`Loaded ${images.length} ${category} items from portfolio metadata (cached)`);
      return images;
    } catch (error) {
      console.error(`Error fetching ${category} from portfolio:`, error);
      return [];
    }
  },
  ['portfolio-category'],
  {
    revalidate: 300, // 5 minutes
    tags: ['portfolio-data'],
  }
);

// Function to get portfolio data by category with enhanced caching
async function getPortfolioByCategory(category: string): Promise<ImageItem[]> {
  return getCachedPortfolioByCategory(category, CACHE_VERSION);
}

// Enhanced cached function for all portfolio data
const getCachedPortfolioData = unstable_cache(
  async (version: string) => {
    console.log('Fetching fresh portfolio data...');
    
    // Get initial data for server-side rendering, with parallel requests for better performance
    const [cards, fliers, letterheads, logos, profiles, branding] = await Promise.all([
      getPortfolioByCategory('cards'),
      getPortfolioByCategory('fliers'),
      getPortfolioByCategory('letterheads'),
      getPortfolioByCategory('logos'),
      getPortfolioByCategory('profiles'),
      getPortfolioByCategory('branding')
    ]);

    return { cards, fliers, letterheads, logos, profiles, branding };
  },
  ['portfolio-all-data'],
  {
    revalidate: 300, // 5 minutes
    tags: ['portfolio-data', 'portfolio-all'],
  }
);

export default async function PortfolioPage() {
  let portfolioData: {
    cards: ImageItem[];
    fliers: ImageItem[];
    letterheads: ImageItem[];
    logos: ImageItem[];
    profiles: ImageItem[];
    branding: ImageItem[];
  } = {
    cards: [],
    fliers: [],
    letterheads: [],
    logos: [],
    profiles: [],
    branding: []
  };

  try {
    // Use cached data with version for cache busting
    portfolioData = await getCachedPortfolioData(CACHE_VERSION);
    console.log('Portfolio data loaded from cache');
  } catch (error) {
    console.error('Error loading portfolio data:', error);
    // Continue with empty arrays - the page will show "No images available" messages
  }

  // Shuffle each array to randomize display
  const shuffledCards = shuffleArray(portfolioData.cards);
  const shuffledFliers = shuffleArray(portfolioData.fliers);
  const shuffledLetterheads = shuffleArray(portfolioData.letterheads);
  const shuffledLogos = shuffleArray(portfolioData.logos);
  const shuffledProfiles = shuffleArray(portfolioData.profiles);
  const shuffledBranding = shuffleArray(portfolioData.branding);

  // Select a few random items for the featured section, ensuring we have items
  const allItems = [
    ...shuffledCards.slice(0, 2),
    ...shuffledFliers.slice(0, 2),
    ...shuffledLetterheads.slice(0, 2),
    ...shuffledLogos.slice(0, 2),
    ...shuffledProfiles.slice(0, 2),
    ...shuffledBranding.slice(0, 2)
  ].filter(item => item && item.src); // Filter out any invalid items

  const featuredItems = shuffleArray(allItems).slice(0, 8);

  return (
    <main className="pt-16 md:pt-20 bg-white">
      {/* Enhanced meta tags for cache control */}
      <meta httpEquiv="Cache-Control" content="public, max-age=300, stale-while-revalidate=3600" />
      
      {/* Hero Section with gradient background matching catalogue page */}
      <section className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-1 bg-[#FF5400]"></div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Portfolio</h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8">
              Explore our creative work showcasing design excellence and innovative solutions
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="#logos"
                className="px-6 py-3 bg-white text-[#0A1929] hover:bg-blue-50 rounded-full font-medium transition-colors"
              >
                Logos
              </Link>
              <Link
                href="#branding"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Branding
              </Link>
              <Link
                href="#fliers"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Fliers
              </Link>
              <Link
                href="#cards"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Cards
              </Link>
              <Link
                href="#letterheads"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Letterheads
              </Link>
              <Link
                href="#profiles"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Profiles
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Portfolio Section - Visually Appealing Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">Featured Projects</h2>

          <Suspense fallback={<div className="py-20 text-center"><LoadingSpinner size="lg" /></div>}>
            {featuredItems.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredItems.map((image, index) => (
                  <div
                    key={`featured-${index}`}
                    className="group relative aspect-square overflow-hidden rounded-xl bg-gray-100 shadow-sm hover:shadow-md transition-all duration-300"
                  >
                    {/* Static placeholder that's immediately visible */}
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl"></div>

                    {/* Image with priority for first 4 items to improve LCP */}
                    <Image
                      src={image.src}
                      alt={image.alt || 'Portfolio piece'}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1280px) 25vw, 25vw"
                      className="object-cover transition-all group-hover:scale-105 duration-500"
                      loading={index < 4 ? "eager" : "lazy"}
                      quality={75}
                      fetchPriority={index < 2 ? "high" : "auto"}
                    />

                    {/* Overlay with info */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                      <h3 className="text-white text-lg font-medium">{image.alt}</h3>
                      <p className="text-white/80 text-sm capitalize">
                        {image.category}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-20 text-center text-gray-500">
                <p className="text-lg">Featured projects will appear here once portfolio images are loaded.</p>
              </div>
            )}
          </Suspense>
        </div>
      </section>

      {/* Logo Design Section - Client Component for refresh functionality */}
      <section id="logos" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Logo Design</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional logos that establish brand identity and make lasting impressions
          </p>

          <ClientGallerySection
            items={shuffledLogos}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="logos"
          />
        </div>
      </section>

      {/* Branding Section - Client Component for refresh functionality */}
      <section id="branding" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Branding & Identity</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Comprehensive branding solutions that communicate your unique identity and values
          </p>

          <ClientGallerySection
            items={shuffledBranding}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="branding"
          />
        </div>
      </section>

      {/* Fliers Section - Client Component for refresh functionality */}
      <section id="fliers" className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-1 bg-gradient-to-r from-[#FF5400] to-[#FF7A3A] rounded-full"></div>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#1a2942] to-[#2c4463] bg-clip-text text-transparent">
              Fliers & Marketing Materials
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto mb-8 leading-relaxed">
              Eye-catching fliers and marketing materials designed to capture attention and drive engagement. 
              Our designs support both portrait and square formats to maximize impact across all platforms.
            </p>
            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Print Ready</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Digital Formats</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Social Media</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Square & Portrait</span>
            </div>
          </div>

                     <ClientGallerySection
             items={shuffledFliers}
             gridCols="grid-cols-2 sm:grid-cols-3 md:grid-cols-4"
             aspectRatio="aspect-square"
             objectFit="object-cover"
             category="fliers"
           />
        </div>
      </section>

      {/* Business Cards Section - Client Component for refresh functionality */}
      <section id="cards" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Business Cards</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional business cards that make memorable first impressions and represent your brand with style
          </p>

          <ClientGallerySection
            items={shuffledCards}
            gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
            aspectRatio="aspect-[5/3]"
            objectFit="object-cover"
            category="cards"
          />
        </div>
      </section>

      {/* Letterheads Section - Client Component for refresh functionality */}
      <section id="letterheads" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Letterheads & Stationery</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional letterheads and stationery designs that enhance your business communications
          </p>

          <ClientGallerySection
            items={shuffledLetterheads}
            gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="letterheads"
          />
        </div>
      </section>

      {/* Company Profiles Section - Client Component for refresh functionality */}
      <section id="profiles" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Company Profiles</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Comprehensive company profiles that showcase your business story, values, and capabilities
          </p>

          <ClientGallerySection
            items={shuffledProfiles}
            gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="profiles"
          />
        </div>
      </section>
    </main>
  );
}

export const metadata = {
  title: 'Portfolio | Mocky Digital Kenya',
  description: 'Browse our diverse portfolio of logos, branding, fliers, business cards, letterheads, and company profiles.',
  keywords: 'logo design, branding, identity design, fliers, business cards, letterheads, company profiles, graphic design, kenya, nairobi'
};