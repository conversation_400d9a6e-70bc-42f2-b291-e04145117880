@tailwind base;
@tailwind components;
@tailwind utilities;

/* Your custom CSS below */

/* Hide scrollbar for clean UI */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scroll-behavior: smooth; /* Smooth scrolling */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Snap scrolling */
.snap-x {
  scroll-snap-type: x mandatory;
}
.snap-start {
  scroll-snap-align: start;
}

/* Enhanced scrollbar styling */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Product page animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes for animations */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

/* Fix for navbar overlap with content */
.page-header-padding {
  padding-top: 7rem !important; /* Add extra padding to prevent content from being hidden under navbar */
}
:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
  
  /* Updated Theme Colors */
  --primary: #0A2647;
  --secondary: #FF5400;
  --accent: #205295;
  --primary-rgb: 10, 38, 71;
  --secondary-rgb: 255, 84, 0;
  --accent-rgb: 32, 82, 149;
  
  /* Legacy colors for compatibility */
  --primary-dark: #0051b3;
  --primary-color: #0A2647;
  --secondary-color: #FF5400;
  --accent-color: #205295;
  --accent-hover: #1976d2;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --text-color: #f8fafc;
  --light-text: #f8fafc;
  --background: #ffffff;
  --light-background: #f8fafc;
  --card-background: #ffffff;
  --border-color: #e2e8f0;
  --border-radius: 0.5rem;
  --transition: all 0.3s ease-in-out;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes blob {
  0% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0, 0) scale(1);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
  }
}

@layer base {
  body {
    @apply bg-gray-50 min-h-screen antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight text-orange-500;
  }

  p {
    @apply text-gray-700;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Blog Styles */
.blog-content h1 {
  @apply text-3xl font-bold text-gray-900 mb-6 mt-8;
}

.blog-content h2 {
  @apply text-2xl font-bold text-gray-900 mb-4 mt-8;
}

.blog-content h3 {
  @apply text-xl font-bold text-gray-900 mb-3 mt-6;
}

.blog-content p {
  @apply text-gray-700 mb-5 leading-relaxed;
}

.blog-content ul, .blog-content ol {
  @apply mb-6 pl-6;
}

.blog-content ul {
  @apply list-disc;
}

.blog-content ol {
  @apply list-decimal;
}

.blog-content li {
  @apply mb-2 text-gray-700;
}

.blog-content a {
  @apply text-gray-900 font-medium underline hover:text-black;
}

.blog-content blockquote {
  @apply border-l-2 border-gray-900 pl-4 italic my-6 text-gray-700 py-1;
}

.blog-content img {
  @apply rounded-lg my-6 max-w-full h-auto;
}

.blog-content pre {
  @apply bg-gray-100 text-gray-800 p-4 rounded-sm overflow-x-auto my-6;
}

.blog-content code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded-sm text-sm font-mono;
}

@layer components {
  .container {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Theme color utilities */
  .bg-primary {
    background-color: var(--primary);
  }
  
  .bg-secondary {
    background-color: var(--secondary);
  }
  
  .bg-accent {
    background-color: var(--accent);
  }
  
  .text-primary {
    color: var(--primary);
  }
  
  .text-secondary {
    color: var(--secondary);
  }
  
  .text-accent {
    color: var(--accent);
  }
  
  .border-primary {
    border-color: var(--primary);
  }
  
  .border-secondary {
    border-color: var(--secondary);
  }
  
  .border-accent {
    border-color: var(--accent);
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Custom border width utilities */
  .border-3 {
    border-width: 3px;
  }

  /* Enhanced gallery styles */
  .gallery-item {
    @apply relative aspect-square bg-white rounded-2xl overflow-hidden cursor-pointer shadow-sm hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 hover:border-gray-200;
  }

  .gallery-overlay {
    @apply absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 transition-all duration-500 flex flex-col justify-between p-4;
  }

  .gallery-item:hover .gallery-overlay {
    @apply opacity-100;
  }

  /* Category Navigation */
  @media (max-width: 768px) {
    .category-navigation {
      @apply hidden;
    }
  }

  /* Header & Navigation */
  .header {
    @apply fixed top-0 left-0 w-full z-50 transition-all duration-300 bg-white bg-opacity-80 backdrop-blur-lg;
  }

  .header.scrolled {
    @apply shadow-lg bg-white bg-opacity-90;
  }

  .navbar {
    @apply flex items-center justify-between py-4;
  }

  .logo {
    @apply flex items-center gap-3;
  }

  .logo:hover {
    @apply opacity-90 transition-opacity;
  }

  .logo a {
    @apply flex items-center gap-2 text-xl font-bold text-primary;
  }

  .nav-menu {
    @apply flex items-center gap-8;
  }

  .nav-menu a {
    @apply text-gray-700 hover:text-blue-600 transition-colors relative;
  }

  .nav-menu a::after {
    @apply content-[''] absolute left-0 bottom-0 w-0 h-0.5 bg-blue-600 transition-all duration-300;
  }

  .nav-menu a:hover::after {
    @apply w-full;
  }

  .nav-menu a.active {
    @apply text-primary font-medium;
  }

  .nav-menu a.active::after {
    @apply w-full;
  }

  /* Mobile Navigation */
  @media (max-width: 1023px) {
    .nav-menu {
      @apply fixed top-0 -right-full w-64 h-screen bg-white shadow-lg flex flex-col items-start pt-20 px-6 transition-all duration-300 ease-in-out;
    }

    .nav-menu.active {
      @apply right-0;
    }

    .nav-menu li {
      @apply w-full border-b border-gray-100 last:border-none;
    }

    .nav-menu a {
      @apply block py-4 text-gray-800 hover:text-orange-500 transition-colors w-full;
    }

    .nav-toggle {
      @apply block fixed right-4 top-6 z-[60] w-10 h-10 bg-transparent border-0 cursor-pointer;
    }

    .hamburger {
      @apply relative w-6 h-5;
    }

    .hamburger span {
      @apply absolute left-0 w-full h-0.5 bg-gray-700 transition-all duration-300 rounded-full;
    }

    .hamburger span:first-child {
      @apply top-0;
    }

    .hamburger span:nth-child(2) {
      @apply top-1/2 -translate-y-1/2;
    }

    .hamburger span:last-child {
      @apply bottom-0;
    }

    .hamburger.active span:first-child {
      @apply top-1/2 -translate-y-1/2 rotate-45;
    }

    .hamburger.active span:nth-child(2) {
      @apply opacity-0;
    }

    .hamburger.active span:last-child {
      @apply bottom-1/2 translate-y-1/2 -rotate-45;
    }
  }

  @media (min-width: 1024px) {
    .nav-toggle {
      @apply hidden;
    }

    .nav-menu {
      @apply flex items-center gap-8;
    }

    .nav-menu a {
      @apply text-gray-700 hover:text-blue-600 transition-colors relative;
    }

    .nav-menu a::after {
      @apply content-[''] absolute left-0 bottom-0 w-0 h-0.5 bg-blue-600 transition-all duration-300;
    }

    .nav-menu a:hover::after {
      @apply w-full;
    }

    .nav-menu > li:hover > ul {
      @apply opacity-100 visible translate-y-0;
    }

    .nav-menu ul {
      @apply absolute top-full left-0 min-w-[200px] bg-white shadow-lg rounded-lg
      opacity-0 invisible translate-y-2 transition-all duration-300 border border-gray-100
      p-1;
    }

    .nav-menu li > a,
    .nav-menu li > button {
      @apply block px-4 py-2 rounded-lg text-gray-700 hover:text-primary hover:bg-gray-50
      transition-all whitespace-nowrap;
    }
  }

  /* Hero Section */
  .hero {
    @apply bg-primary relative min-h-screen;
  }

  .hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5rem;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23f8fafc' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E") bottom center/cover no-repeat;
  }

  .hero-wrapper {
    @apply container grid lg:grid-cols-2 gap-12 items-center relative z-10;
  }

  .floating-shapes {
    @apply absolute inset-0 overflow-hidden pointer-events-none;
  }

  .shape {
    @apply absolute rounded-full mix-blend-multiply filter blur-xl opacity-70;
  }

  .shape-1 {
    @apply w-72 h-72 -top-10 -right-10;
    background-color: rgba(33, 150, 243, 0.6);
    animation: float 8s infinite;
  }

  .shape-2 {
    @apply w-96 h-96 top-1/2 right-1/4;
    background-color: rgba(13, 71, 161, 0.6);
    animation: float 12s infinite;
  }

  .shape-3 {
    @apply w-80 h-80 bottom-1/4 right-1/3;
    background-color: rgba(26, 35, 126, 0.6);
    animation: float 10s infinite;
  }

  .hero-content {
    @apply relative z-10;
  }

  .hero-tag {
    @apply animate-fadeIn;
  }

  .hero-title {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6;
  }

  .hero-description {
    @apply text-base sm:text-lg md:text-xl text-gray-200 mb-6 sm:mb-8 max-w-xl mx-auto lg:mx-0;
  }

  .hero-buttons {
    @apply flex flex-col sm:flex-row gap-4 justify-center lg:justify-start;
  }

  /* Buttons */
  .cta-button, .secondary-button {
    @apply relative overflow-hidden;
  }

  .cta-button::before, .secondary-button::before {
    content: '';
    @apply absolute inset-0 bg-white/20 transform scale-x-0 origin-left transition-transform duration-300;
  }

  .cta-button:hover::before, .secondary-button:hover::after {
    @apply scale-x-100;
  }

  .cta-button {
    @apply bg-accent hover:bg-accent-hover text-white font-semibold px-8 py-4 rounded-full transition-colors;
    box-shadow: 0 4px 14px rgba(33, 150, 243, 0.4);
  }

  .cta-button:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.5);
  }

  .cta-button:active {
    @apply transform translate-y-0;
    box-shadow: 0 2px 10px rgba(33, 150, 243, 0.4);
  }

  .secondary-button {
    @apply bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-full transition-colors;
  }

  .secondary-button:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
  }

  /* Service Cards */
  .service-card {
    @apply relative bg-white rounded-2xl p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl overflow-hidden flex flex-col h-full;
  }

  .service-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-500;
    background: linear-gradient(to bottom right, rgba(26, 35, 126, 0.1), rgba(33, 150, 243, 0.1));
  }

  .service-card:hover::before {
    @apply opacity-100;
  }

  .service-card .icon-wrapper {
    @apply relative w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-500 z-10;
    background-color: rgba(26, 35, 126, 0.1);
  }

  .service-card:hover .icon-wrapper {
    @apply transform scale-110 rotate-3;
  }

  .service-card .icon {
    @apply text-2xl text-primary transition-all duration-500;
  }

  .service-card:hover .icon {
    @apply text-accent;
  }

  .service-card h3 {
    @apply text-xl font-semibold mb-4 transition-all duration-500;
  }

  .service-card:hover h3 {
    @apply text-accent;
  }

  .service-card p {
    @apply relative text-gray-600 mb-6 z-10;
  }

  .service-content {
    @apply flex flex-col flex-grow;
  }

  .service-features {
    @apply mt-6 space-y-3 flex-grow;
  }

  .feature-item {
    @apply flex items-center gap-2 text-gray-600 transition-all duration-300;
  }

  .service-card:hover .feature-item {
    @apply text-gray-700;
  }

  .service-link {
    @apply inline-flex items-center gap-2 text-primary font-semibold mt-6 transition-all duration-300 w-full justify-center bg-gray-50 py-3 rounded-xl hover:bg-gray-100;
  }

  .service-link:hover {
    @apply text-accent;
  }

  .service-link i {
    @apply transition-transform duration-300;
  }

  .service-link:hover i {
    @apply transform translate-x-1;
  }

  /* Portfolio Cards */
  .portfolio-card {
    @apply bg-white rounded-xl shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl overflow-hidden;
  }

  .portfolio-card h4 {
    @apply text-gray-900 transition-colors duration-300;
  }

  .portfolio-card:hover h4 {
    @apply text-primary;
  }

  .portfolio-card p {
    @apply text-gray-600;
  }

  .portfolio-card a {
    @apply font-semibold;
  }

  .portfolio-card a i {
    @apply transition-transform duration-300;
  }

  .portfolio-card a:hover i {
    @apply transform translate-x-1;
  }

  /* Price Tag */
  .price-tag {
    @apply mt-6 text-center;
  }

  .price-tag .amount {
    @apply text-2xl sm:text-3xl;
  }

  .price-tag .currency {
    @apply text-lg text-gray-600 font-normal;
  }

  .price-tag .period {
    @apply text-sm text-gray-500;
  }

  /* Section Headers */
  .section-header {
    @apply text-center mb-8 sm:mb-12 lg:mb-16;
  }

  .section-header h2 {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4;
  }

  .section-header p {
    @apply text-base sm:text-lg lg:text-xl text-gray-600;
  }

  /* Grid Layout */
  .services-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8;
  }

  /* Responsive Images */
  .responsive-image {
    @apply w-full h-48 sm:h-56 lg:h-64;
  }

  /* Custom Shadows */
  .hover-shadow {
    @apply transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10;
  }

  /* Gradient Backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
  }

  /* Custom Animations */
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Glassmorphism */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-lg border border-white border-opacity-20 shadow-lg;
  }

  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-blue-500 bg-opacity-50 rounded-full hover:bg-blue-600 hover:bg-opacity-50;
  }

  /* Navigation */
  .nav-link {
    @apply relative px-4 py-2 text-gray-700 hover:text-blue-600 transition-colors duration-300 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-600 after:transition-all after:duration-300 hover:after:w-full;
  }

  .nav-link.active {
    @apply text-blue-600 after:w-full;
  }

  /* Pricing Cards */
  .pricing-card {
    @apply relative bg-white rounded-2xl p-6 sm:p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl border border-gray-100;
  }

  .pricing-card.popular {
    @apply scale-100 lg:scale-105;
  }

  .pricing-card .popular-tag {
    @apply absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-6 py-1 rounded-full text-sm font-medium;
  }

  /* Footer */
  .footer {
    @apply bg-gray-900 text-white py-16;
  }

  .footer-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12;
  }

  .footer-link {
    @apply text-gray-400 hover:text-white transition-colors duration-300;
  }

  /* Custom Utilities */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent;
  }

  .backdrop-blur {
    @apply backdrop-blur-lg bg-white/80;
  }

  /* Glassmorphism styles for catalogue cards */
  .glassmorphism-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glassmorphism-card:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    transform: translateY(-8px) scale(1.02);
  }

  .glassmorphism-footer {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glassmorphism-button {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .glassmorphism-button:hover {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .glassmorphism-tag {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.4);
  }

  /* Move all media queries to the end of the components layer */
  @media (max-width: 1023px) {
    .nav-toggle {
      @apply block;
    }

    .nav-menu {
      @apply fixed top-0 right-0 w-64 h-screen bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out pt-20 px-6;
    }

    .nav-menu.active {
      @apply translate-x-0;
    }

    .nav-menu li {
      @apply block mb-4;
    }

    .nav-menu a {
      @apply block py-2 text-gray-800 hover:text-orange-500;
    }

    .hero-wrapper {
      @apply pt-20 pb-12;
    }

    .hero-content {
      @apply text-center;
    }

    .hero-buttons {
      @apply flex-col items-stretch;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 1s ease-out;
  }

  .animate-slideUp {
    animation: slideUp 1s ease-out 0.5s both;
  }

  /* Responsive shapes */
  @media (max-width: 768px) {
    .shape-1 {
      @apply w-48 h-48 -top-6 -right-6;
    }

    .shape-2 {
      @apply w-64 h-64;
    }

    .shape-3 {
      @apply w-56 h-56;
    }
  }

  /* Responsive pricing cards */
  .pricing-card {
    @apply p-6 sm:p-8;
  }

  .pricing-card.popular {
    @apply scale-100 lg:scale-105;
  }

  /* Responsive images */
  .responsive-image {
    @apply w-full h-48 sm:h-56 lg:h-64;
  }

  /* Responsive hero section */
  .hero {
    @apply min-h-[calc(100vh-72px)] py-12 sm:py-16 lg:py-0;
  }

  /* Responsive features list */
  .feature-item {
    @apply text-sm sm:text-base;
  }

  /* Responsive price tag */
  .price-tag .amount {
    @apply text-2xl sm:text-3xl;
  }

  /* Responsive service features */
  .service-features {
    @apply grid grid-cols-1 gap-2 sm:gap-3;
  }

  /* Responsive animations */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-fadeIn,
    .animate-slideUp {
      animation: none;
    }
  }

  /* Improved mobile menu animation */
  @keyframes menuSlideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .nav-menu.active {
    animation: menuSlideDown 0.3s ease-out forwards;
  }

  /* Responsive contact form */
  .contact-form {
    @apply w-full max-w-xl mx-auto;
  }

  .contact-form input,
  .contact-form textarea,
  .contact-form select {
    @apply w-full px-4 py-3 text-base sm:text-lg;
  }

  /* Responsive CTA sections */
  .cta {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .cta h2 {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .cta p {
    @apply text-base sm:text-lg lg:text-xl;
  }

  /* Add these to your existing styles */

  .nav-menu li {
    position: relative;
  }

  .nav-menu li > a,
  .nav-menu li > button {
    display: block;
    padding: 0.5rem 1rem;
    color: inherit;
    text-decoration: none;
    transition: color 0.2s;
  }

  .nav-menu li > button {
    background: none;
    border: none;
    cursor: pointer;
    font: inherit;
    width: 100%;
    text-align: left;
  }

  /* Desktop styles */
  @media (min-width: 1024px) {
    .nav-menu > li:hover > ul {
      display: block;
    }

    .nav-menu ul {
      position: absolute;
      top: 100%;
      left: 0;
      min-width: 200px;
      background: white;
      box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
      border-radius: 0.5rem;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s;
    }

    .nav-menu li:hover > ul {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }

  /* Mobile styles */
  @media (max-width: 1023px) {
    .nav-menu ul {
      background: rgba(0,0,0,0.05);
      margin: 0.5rem 0;
      border-radius: 0.5rem;
    }
  }

  /* Add to your existing animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes blob {
    0% {
      transform: translate(0, 0) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0, 0) scale(1);
    }
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes particle-float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    100% {
      transform: translateY(-100vh) rotate(360deg);
    }
  }

  /* Add these utility classes */
  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animate-gradient {
    background: linear-gradient(to right, #ff7e33, #ff4b1f);
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Particle styles */
  .particle {
    opacity: 0.6;
    animation: particle-float linear infinite;
  }

  /* Add to your existing animations */
  @keyframes fadeInOut {
    0% {
      opacity: 0;
      transform: scale(0.9) rotate(6deg) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) rotate(0) translateY(0);
    }
  }

  .fade-in-out {
    animation: fadeInOut 0.5s ease-in-out forwards;
  }

  /* Update/Add these animations */
  @keyframes float {
    0% {
      transform: translateY(100vh) scale(0.8) rotate(0deg);
    }
    20% {
      transform: translateY(80vh) scale(1) rotate(45deg);
    }
    100% {
      transform: translateY(-20vh) scale(0.8) rotate(90deg);
    }
  }

  @keyframes glow {
    0% {
      box-shadow: 0 0 5px rgba(255, 255, 255, 0.1),
                  0 0 10px rgba(255, 255, 255, 0.1);
    }
    100% {
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.3),
                  0 0 20px rgba(255, 255, 255, 0.2);
    }
  }

  /* Update particle styles */
  .particle {
    will-change: transform;
    transform: translateZ(0);
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
  }

  /* Add these new utility classes */
  .animate-float-slow {
    animation: float 35s linear infinite;
  }

  .animate-float-medium {
    animation: float 30s linear infinite;
  }

  .animate-float-fast {
    animation: float 25s linear infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* Add to your existing animations */
  @keyframes ripple {
    0% {
      transform: translate(-50%, -50%) scale(0);
      opacity: 0.5;
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0;
    }
  }

  .ripple {
    animation: ripple 1s ease-out forwards;
    will-change: transform, opacity;
  }

  /* Optional: Add a subtle gradient to the ripples */
  .ripple-gradient {
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 70%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  /* Add these new animations and styles */
  .bg-gradient-mesh {
    background-color: #0c1d48;
    background-image:
      radial-gradient(at 40% 20%, rgba(28,99,242,0.15) 0px, transparent 50%),
      radial-gradient(at 80% 0%, rgba(33,150,243,0.15) 0px, transparent 50%),
      radial-gradient(at 0% 50%, rgba(50,108,255,0.15) 0px, transparent 50%),
      radial-gradient(at 80% 50%, rgba(76,81,191,0.15) 0px, transparent 50%),
      radial-gradient(at 0% 100%, rgba(28,99,242,0.15) 0px, transparent 50%),
      radial-gradient(at 80% 100%, rgba(33,150,243,0.15) 0px, transparent 50%),
      radial-gradient(at 0% 0%, rgba(50,108,255,0.15) 0px, transparent 50%);
    animation: gradientShift 15s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Update Animated Lines styles */
  .lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    margin: auto;
    width: 100vw;
    z-index: 1;
  }

  .line {
    position: absolute;
    width: 1px;
    height: 100%;
    top: 0;
    left: 50%;
    background: rgba(255, 255, 255, 0.05);
    overflow: hidden;
    opacity: 0.3;
  }

  .line::after {
    content: '';
    display: block;
    position: absolute;
    height: 15vh;
    width: 100%;
    top: -50%;
    left: 0;
    background: linear-gradient(to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.5) 75%,
      rgba(255, 255, 255, 0.7) 100%
    );
    animation: drop 7s 0s infinite;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.4, 0.26, 0, 0.97);
  }

  .line:nth-child(1) { margin-left: -20%; }
  .line:nth-child(2) { margin-left: 0%; }
  .line:nth-child(3) { margin-left: 20%; }
  .line:nth-child(4) { margin-left: -30%; }
  .line:nth-child(5) { margin-left: 30%; }
  .line:nth-child(6) { margin-left: -40%; }

  .line:nth-child(1)::after { animation-delay: 2s; }
  .line:nth-child(2)::after { animation-delay: 1s; }
  .line:nth-child(3)::after { animation-delay: 3s; }
  .line:nth-child(4)::after { animation-delay: 2.5s; }
  .line:nth-child(5)::after { animation-delay: 1.5s; }
  .line:nth-child(6)::after { animation-delay: 3.5s; }

  @keyframes drop {
    0% {
      top: -50%;
    }
    100% {
      top: 110%;
    }
  }

  .line::after {
    animation-duration: 10s;
  }

  /* Add these new animations */
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Add these utility classes */
  .animate-slideUp {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  /* Optional: Add hover effects for inputs */
  input:hover, select:hover, textarea:hover {
    border-color: theme('colors.gray.300');
  }

  /* Add focus transition for all form elements */
  input, select, textarea, button {
    transition: all 0.2s ease-in-out;
  }

  /* Add these new animations */
  @keyframes bounce-slow {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-bounce-slow {
    animation: bounce-slow 3s ease-in-out infinite;
  }

  /* Update existing animations for smoother transitions */
  .animate-fadeInUp {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fadeIn {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary-dark {
    background-color: var(--primary-dark);
  }

  .hover\:bg-primary-dark:hover {
    background-color: var(--primary-dark);
  }
}

/* 404 Page Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-fade-in-delay-200 {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.2s forwards;
}

.animate-fade-in-delay-400 {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.4s forwards;
}

.animate-fade-in-delay-600 {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.6s forwards;
}

/* Mobile touch improvements for admin hamburger menu */
@media (max-width: 768px) {
  .admin-mobile-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    user-select: none;
    min-height: 44px;
    min-width: 44px;
    position: relative;
    z-index: 60;
  }
  
  .admin-mobile-sidebar {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y;
    top: 80px !important;
    height: calc(100vh - 80px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .admin-mobile-sidebar-content {
    padding: 0.5rem;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
  }

  .admin-mobile-sidebar-content::-webkit-scrollbar {
    width: 4px;
  }

  .admin-mobile-sidebar-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .admin-mobile-sidebar-content::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
  }
  
  .admin-mobile-overlay {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    pointer-events: auto;
    user-select: none;
    top: 80px !important;
    height: calc(100vh - 80px) !important;
  }
}

/* Mobile touch improvements for main site hamburger menu */
@media (max-width: 1024px) {
  .mobile-toggle {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    user-select: none;
    min-height: 44px;
    min-width: 44px;
    position: relative;
    z-index: 60;
  }
  
  .mobile-toggle:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
  
  /* Main site mobile menu enhancements */
  [data-testid="mobile-menu"] {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y;
  }
  
  [data-testid="mobile-overlay"] {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    pointer-events: auto;
    user-select: none;
  }
}

/* Improve hamburger menu visibility and interactions */
.hamburger-menu-button,
.mobile-toggle {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.hamburger-menu-button:active,
.mobile-toggle:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* Clean Admin Header Styling */
.admin-header {
  z-index: 50;
  height: 64px;
  min-height: 64px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(229, 231, 235, 1);
  transition: all 0.2s ease-out;
}

.admin-mobile-sidebar {
  z-index: 50;
}

.admin-mobile-overlay {
  z-index: 45;
}

/* Ensure proper z-index stacking for main site elements */
.navbar-header {
  z-index: 50;
}

[data-testid="mobile-menu"] {
  z-index: 50;
}

[data-testid="mobile-overlay"] {
  z-index: 45;
}

/* Admin Desktop Sidebar - Below Header Layout */
.admin-desktop-sidebar {
  @apply fixed left-0 w-60 bg-white border-r border-gray-200 transition-all duration-300 ease-in-out;
  top: 64px;
  height: calc(100vh - 64px);
  z-index: 30;
  padding: 1rem 0;
  overflow-y: auto;
  overflow-x: hidden;
  /* Clean scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.admin-desktop-sidebar::-webkit-scrollbar {
  width: 6px;
}

.admin-desktop-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.admin-desktop-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.admin-desktop-sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Prevent text selection during menu interactions */
.mobile-toggle,
.hamburger-menu-button,
.dropdown-toggle button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Smooth animations for menu items */
.nav-menu a,
[data-testid="mobile-menu"] a,
[data-testid="mobile-menu"] button {
  transition: all 0.2s ease-in-out;
}

/* Focus ring improvements for better accessibility */
.focus\:ring-primary:focus {
  --tw-ring-color: rgb(var(--primary) / var(--tw-ring-opacity, 0.5));
}

/* Ensure dropdown animations are smooth */
.dropdown-toggle > div {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

/* Admin grid stability and transitions */
.admin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  grid-auto-rows: min-content;
  transition: all 0.3s ease-in-out;
}

/* Responsive admin grid improvements */
@media (max-width: 640px) {
  .admin-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .admin-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.25rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .admin-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .admin-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
  }
}

.admin-grid-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.admin-grid-item.deleting {
  transform: scale(0.95);
  opacity: 0.5;
  pointer-events: none;
}

.admin-grid-item.deleted {
  transform: scale(0.8);
  opacity: 0;
  height: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Prevent layout shifts during operations */
.admin-portfolio-grid {
  min-height: 400px;
  contain: layout style;
}

@supports (container-type: inline-size) {
  .admin-portfolio-grid {
    container-type: inline-size;
  }
}

/* Enhanced admin layout responsive improvements */
.admin-layout-responsive {
  --admin-sidebar-width: 256px;
  --admin-header-height: 64px;
  --admin-content-padding: 1rem;
  --admin-content-gap: 1rem;
  --admin-border-radius: 0.5rem;
  --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Ensure proper scrolling behavior */
  overflow-x: hidden;
  overflow-y: auto;
  height: 100vh;
  position: relative;
}

/* Fix for main content area scrolling */
.admin-layout-responsive main {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  overscroll-behavior-y: contain; /* Prevent bounce scrolling */
}

@media (min-width: 640px) {
  .admin-layout-responsive {
    --admin-content-padding: 1.5rem;
    --admin-content-gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .admin-layout-responsive {
    --admin-content-padding: 2rem;
    --admin-content-gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .admin-layout-responsive {
    --admin-content-padding: 2.5rem;
    --admin-content-gap: 2.5rem;
  }
}

@media (min-width: 1280px) {
  .admin-layout-responsive {
    --admin-content-padding: 3rem;
    --admin-content-gap: 3rem;
  }
}

/* Enhanced admin card responsiveness with better touch targets */
.admin-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
  padding: var(--admin-content-padding);
  transition: all 0.2s ease-in-out;
  border-radius: var(--admin-border-radius);
  box-shadow: var(--admin-shadow);
}

.admin-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-1px);
}

.admin-card-compact {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
  padding: calc(var(--admin-content-padding) * 0.75);
  transition: all 0.2s ease-in-out;
  border-radius: var(--admin-border-radius);
  box-shadow: var(--admin-shadow);
}

/* Enhanced admin card variants with brand colors */
.admin-card-interactive {
  @apply cursor-pointer transition-all duration-200;
}

.admin-card-interactive:hover {
  @apply shadow-lg transform -translate-y-1;
}

.admin-card-stat {
  @apply flex items-center space-x-4 p-6;
}

/* Admin header theming */
.admin-header-icon {
  @apply p-3 rounded-lg;
}

/* Admin badges with brand colors */
.admin-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

/* Enhanced admin table responsive improvements */
.admin-table-container {
  @apply w-full overflow-x-auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-x: contain;
  border-radius: var(--admin-border-radius);
}

@media (max-width: 768px) {
  .admin-table-container {
    @apply rounded-lg border border-gray-200;
    margin: calc(var(--admin-content-padding) * -0.5);
    padding: calc(var(--admin-content-padding) * 0.5);
  }
}

.admin-table {
  @apply w-full divide-y divide-gray-200;
  min-width: 650px; /* Increased minimum width for better readability */
}

@media (max-width: 768px) {
  .admin-table {
    font-size: 0.875rem; /* Smaller text on mobile */
    min-width: 580px; /* Adjusted for mobile */
  }
}

/* Enhanced admin form responsiveness with better spacing */
.admin-form-grid {
  display: grid;
  gap: var(--admin-content-gap);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .admin-form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .admin-form-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.admin-form-grid.admin-form-grid-full {
  grid-template-columns: 1fr;
}

.admin-form-grid.admin-form-grid-2-col {
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .admin-form-grid.admin-form-grid-2-col {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Enhanced admin dashboard stats responsive grid */
.admin-stats-grid {
  display: grid;
  gap: var(--admin-content-gap);
  grid-template-columns: 1fr;
}

@media (min-width: 480px) {
  .admin-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .admin-stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .admin-stats-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Enhanced admin content list grid */
.admin-content-grid {
  display: grid;
  gap: var(--admin-content-gap);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .admin-content-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-content-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1536px) {
  .admin-content-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Enhanced admin content area responsive improvements */
.admin-content {
  gap: var(--admin-content-gap);
  max-width: 100%;
  overflow-x: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  padding: var(--admin-content-padding);
}

/* Enhanced responsive content area */
@media (max-width: 768px) {
  .admin-content {
    margin-left: 0 !important;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 769px) {
  .admin-content {
    transition: margin-left 0.3s ease-in-out;
  }
}

/* Enhanced admin buttons responsive improvements with better touch targets */
.admin-button-group {
  @apply flex flex-wrap gap-2;
}

@media (max-width: 640px) {
  .admin-button-group {
    @apply flex-col;
  }
  
  .admin-button-group .admin-button {
    @apply w-full justify-center;
    min-height: 44px; /* Apple's recommended touch target size */
  }
}

.admin-button {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium transition-all duration-200;
  min-height: 40px;
  min-width: 40px;
}

@media (max-width: 640px) {
  .admin-button {
    min-height: 44px; /* Larger touch targets on mobile */
    padding: 0.75rem 1rem;
  }
}

/* Enhanced admin button variants with brand colors */
.admin-button-primary {
  @apply bg-primary text-white hover:bg-primary-dark focus:ring-primary;
}

.admin-button-secondary {
  @apply bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary;
}

.admin-button-accent {
  @apply bg-accent text-white hover:bg-accent-dark focus:ring-accent;
}

/* Admin view toggle styling */
.admin-view-toggle {
  @apply flex items-center space-x-1;
}

.admin-view-toggle button {
  @apply flex items-center px-3 py-2 rounded-lg border transition-all duration-200 text-sm font-medium;
}

.admin-button-sm {
  @apply inline-flex items-center justify-center px-3 py-2 border border-transparent rounded text-sm font-medium transition-all duration-200;
  min-height: 36px;
  min-width: 36px;
}

@media (max-width: 640px) {
  .admin-button-sm {
    min-height: 40px;
    padding: 0.5rem 0.75rem;
  }
}

.admin-button-xs {
  @apply inline-flex items-center justify-center px-2 py-1 border border-transparent rounded text-xs font-medium transition-all duration-200;
  min-height: 32px;
  min-width: 32px;
}

@media (max-width: 640px) {
  .admin-button-xs {
    min-height: 36px;
    padding: 0.375rem 0.5rem;
  }
}

/* Enhanced admin mobile table fallback with better card design */
@media (max-width: 768px) {
  .admin-table-mobile-fallback {
    @apply hidden;
  }
  
  .admin-mobile-cards {
    @apply block space-y-4;
  }
  
  .admin-mobile-card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
    padding: var(--admin-content-padding);
    transition: all 0.2s ease-in-out;
  }
  
  .admin-mobile-card:active {
    transform: scale(0.98);
  }
  
  .admin-mobile-card-header {
    @apply flex justify-between items-start mb-3 pb-3 border-b border-gray-100;
  }
  
  .admin-mobile-card-title {
    @apply font-medium text-gray-900 text-base leading-tight;
  }
  
  .admin-mobile-card-subtitle {
    @apply text-sm text-gray-500 mt-1;
  }
  
  .admin-mobile-card-content {
    @apply space-y-3 text-sm text-gray-600;
  }
  
  .admin-mobile-card-content-item {
    @apply flex justify-between items-start py-1;
  }
  
  .admin-mobile-card-content-label {
    @apply font-medium text-gray-700 mr-2 flex-shrink-0;
  }
  
  .admin-mobile-card-content-value {
    @apply text-gray-900 text-right;
  }
  
  .admin-mobile-card-actions {
    @apply flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-100;
  }
}

@media (min-width: 769px) {
  .admin-mobile-cards {
    @apply hidden;
  }
  
  .admin-table-mobile-fallback {
    @apply block;
  }
}

/* Enhanced admin sidebar responsive improvements */
@media (max-width: 768px) {
  .admin-sidebar-content {
    padding: 0.75rem;
  }
  
  .admin-sidebar-item {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
    min-height: 44px; /* Better touch targets */
    display: flex;
    align-items: center;
  }
  
  .admin-sidebar-submenu {
    padding-left: 2rem;
  }
  
  .admin-sidebar-submenu .admin-sidebar-item {
    min-height: 40px;
    padding: 0.75rem 1rem;
  }
}

/* Enhanced admin header responsive improvements */
@media (max-width: 640px) {
  .admin-header-title {
    font-size: 1.25rem;
    line-height: 1.5;
  }
  
  .admin-header-subtitle {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .admin-header-title {
    font-size: 1.125rem;
  }
  
  .admin-header-subtitle {
    font-size: 0.8125rem;
  }
}

/* Enhanced admin pagination responsive improvements */
.admin-pagination {
  @apply flex items-center justify-between flex-wrap gap-4;
}

@media (max-width: 640px) {
  .admin-pagination {
    @apply flex-col space-y-3;
  }
  
  .admin-pagination-info {
    @apply order-2 text-center;
  }
  
  .admin-pagination-controls {
    @apply order-1 w-full flex justify-center;
  }
}

/* Enhanced admin filters responsive improvements */
.admin-filters {
  @apply grid gap-4;
  grid-template-columns: 1fr;
}

@media (min-width: 480px) {
  .admin-filters {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .admin-filters {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-filters {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .admin-filters {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Enhanced admin search bar responsive improvements */
.admin-search-container {
  @apply relative w-full;
}

.admin-search-input {
  @apply w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg text-sm;
  @apply focus:ring-2 focus:ring-primary focus:border-primary;
  @apply transition-all duration-200;
}

@media (max-width: 640px) {
  .admin-search-input {
    @apply py-3.5; /* Larger touch target on mobile */
  }
}

/* Enhanced admin modal responsive improvements */
.admin-modal {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.admin-modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 transition-opacity;
}

.admin-modal-container {
  @apply flex min-h-screen items-center justify-center p-4;
}

.admin-modal-content {
  @apply relative bg-white rounded-lg shadow-xl max-w-lg w-full;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

@media (max-width: 640px) {
  .admin-modal-content {
    @apply m-2 max-h-[calc(100vh-1rem)];
    width: calc(100vw - 1rem);
    max-width: none;
  }
  
  .admin-modal-container {
    @apply p-2;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .admin-modal-content {
    max-width: calc(100vw - 4rem);
  }
}

/* Enhanced admin loading states */
.admin-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
  border-radius: var(--admin-border-radius);
}

.admin-skeleton-text {
  @apply h-4 bg-gray-200 rounded;
}

.admin-skeleton-title {
  @apply h-6 bg-gray-200 rounded w-3/4;
}

.admin-skeleton-button {
  @apply h-10 bg-gray-200 rounded w-24;
}

@media (max-width: 640px) {
  .admin-skeleton-button {
    @apply h-11 w-full; /* Larger on mobile */
  }
}

/* Enhanced admin focus improvements for better accessibility */
.admin-focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.admin-focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500;
}

/* Enhanced admin utility classes */
.admin-text-truncate {
  @apply truncate max-w-full;
}

@media (max-width: 480px) {
  .admin-text-truncate {
    max-width: 120px;
  }
}

@media (min-width: 481px) and (max-width: 640px) {
  .admin-text-truncate {
    max-width: 150px;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .admin-text-truncate {
    max-width: 200px;
  }
}

/* Enhanced admin container improvements */
.admin-container {
  @apply w-full mx-auto;
  padding-left: var(--admin-content-padding);
  padding-right: var(--admin-content-padding);
  max-width: 1600px;
}

@media (min-width: 640px) {
  .admin-container {
    padding-left: calc(var(--admin-content-padding) * 1.5);
    padding-right: calc(var(--admin-content-padding) * 1.5);
  }
}

@media (min-width: 1024px) {
  .admin-container {
    padding-left: calc(var(--admin-content-padding) * 2);
    padding-right: calc(var(--admin-content-padding) * 2);
  }
}

/* Enhanced admin spacing utilities */
.admin-section-spacing {
  gap: var(--admin-content-gap);
}

@media (min-width: 768px) {
  .admin-section-spacing {
    gap: calc(var(--admin-content-gap) * 1.33);
  }
}

@media (min-width: 1024px) {
  .admin-section-spacing {
    gap: calc(var(--admin-content-gap) * 1.67);
  }
}

/* Enhanced responsive breakpoint utilities */
.admin-show-mobile {
  @apply block;
}

@media (min-width: 769px) {
  .admin-show-mobile {
    @apply hidden;
  }
}

.admin-hide-mobile {
  @apply hidden;
}

@media (min-width: 769px) {
  .admin-hide-mobile {
    @apply block;
  }
}

.admin-show-tablet {
  @apply hidden;
}

@media (min-width: 641px) and (max-width: 1024px) {
  .admin-show-tablet {
    @apply block;
  }
}

.admin-hide-tablet {
  @apply block;
}

@media (min-width: 641px) and (max-width: 1024px) {
  .admin-hide-tablet {
    @apply hidden;
  }
}

.admin-show-desktop {
  @apply hidden;
}

@media (min-width: 1025px) {
  .admin-show-desktop {
    @apply block;
  }
}

.admin-hide-desktop {
  @apply block;
}

@media (min-width: 1025px) {
  .admin-hide-desktop {
    @apply hidden;
  }
}

/* Enhanced admin input and form element improvements */
.admin-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-sm;
  @apply focus:ring-2 focus:ring-primary focus:border-primary;
  @apply transition-all duration-200;
  min-height: 40px;
}

@media (max-width: 640px) {
  .admin-input {
    @apply py-3;
    min-height: 44px; /* Better touch targets on mobile */
  }
}

.admin-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white;
  @apply focus:ring-2 focus:ring-primary focus:border-primary;
  @apply transition-all duration-200;
  min-height: 40px;
}

@media (max-width: 640px) {
  .admin-select {
    @apply py-3;
    min-height: 44px;
  }
}

.admin-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-sm;
  @apply focus:ring-2 focus:ring-primary focus:border-primary;
  @apply transition-all duration-200;
  min-height: 80px;
  resize: vertical;
}

@media (max-width: 640px) {
  .admin-textarea {
    min-height: 100px;
  }
}

/* Enhanced admin label and form group improvements */
.admin-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.admin-form-group {
  @apply space-y-2;
}

@media (max-width: 640px) {
  .admin-form-group {
    @apply space-y-3;
  }
}

/* Enhanced admin error and success states */
.admin-error {
  @apply text-red-600 text-sm mt-1;
}

.admin-success {
  @apply text-green-600 text-sm mt-1;
}

.admin-warning {
  @apply text-yellow-600 text-sm mt-1;
}

.admin-info {
  @apply text-blue-600 text-sm mt-1;
}

/* Enhanced admin notification improvements */
.admin-notification {
  @apply fixed top-4 right-4 max-w-sm w-full z-50;
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
  @apply transform transition-all duration-300 ease-in-out;
  padding: var(--admin-content-padding);
}

@media (max-width: 640px) {
  .admin-notification {
    @apply top-2 right-2 left-2 max-w-none;
  }
}

/* Enhanced admin data visualization responsive improvements */
.admin-chart-container {
  @apply w-full overflow-hidden rounded-lg bg-white shadow-sm border border-gray-200;
  padding: var(--admin-content-padding);
}

@media (max-width: 768px) {
  .admin-chart-container {
    @apply overflow-x-auto;
    min-width: 300px;
  }
}

/* Enhanced admin breadcrumb responsive improvements */
.admin-breadcrumb {
  @apply flex items-center space-x-2 text-sm text-gray-600 mb-4;
}

@media (max-width: 640px) {
  .admin-breadcrumb {
    @apply flex-wrap space-x-1 text-xs;
  }
}

/* Enhanced admin icon sizing for better touch targets */
.admin-icon-sm {
  @apply h-4 w-4;
}

.admin-icon {
  @apply h-5 w-5;
}

.admin-icon-lg {
  @apply h-6 w-6;
}

/* Enhanced admin header styling with brand colors */
.admin-header {
  @apply bg-white border-b border-gray-200 px-6 py-4;
}

.admin-header-content {
  @apply flex items-center justify-between;
}

.admin-header-title {
  @apply flex items-center space-x-4;
}

.admin-header-title h1 {
  @apply text-2xl font-bold;
}

.admin-header-actions {
  @apply flex items-center space-x-4;
}

/* Enhanced admin stats styling */
.admin-stat-icon {
  @apply p-3 rounded-lg;
}

.admin-stat-label {
  @apply text-sm font-medium;
}

.admin-stat-value {
  @apply text-2xl font-bold;
}

/* Enhanced admin card details styling */
.admin-card-header {
  @apply flex items-start justify-between mb-4;
}

.admin-card-title {
  @apply text-lg font-semibold;
}

.admin-card-subtitle {
  @apply text-sm;
}

.admin-card-badges {
  @apply flex items-center space-x-2 mb-3;
}

.admin-card-content {
  @apply mb-4;
}

.admin-card-details {
  @apply space-y-2 mb-4;
}

.admin-detail-item {
  @apply flex items-center space-x-2 text-sm;
}

.admin-card-footer {
  @apply flex items-center justify-between pt-3 border-t border-gray-100;
}

.admin-card-links {
  @apply flex items-center space-x-2;
}

/* Enhanced admin action buttons */
.admin-action-button {
  @apply p-2 rounded-lg transition-colors duration-200;
}

/* Enhanced admin dropdown styling */
.admin-dropdown {
  @apply absolute z-50 min-w-48 py-1 mt-1;
}

.admin-dropdown-item {
  @apply flex items-center px-4 py-2 text-sm transition-colors duration-200;
}

/* Enhanced admin results summary */
.admin-results-summary {
  @apply flex items-center justify-between mb-6 text-sm;
}

/* Enhanced admin filters grid */
.admin-filters-grid {
  @apply grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4;
}

@media (max-width: 640px) {
  .admin-icon-sm {
    @apply h-5 w-5; /* Larger on mobile for better visibility */
  }
  
  .admin-icon {
    @apply h-6 w-6;
  }
  
  .admin-icon-lg {
    @apply h-7 w-7;
  }
}

/* Enhanced admin progress bar responsive improvements */
.admin-progress {
  @apply w-full bg-gray-200 rounded-full overflow-hidden;
  height: 8px;
}

@media (max-width: 640px) {
  .admin-progress {
    height: 10px; /* Slightly larger on mobile */
  }
}

.admin-progress-bar {
  @apply h-full bg-blue-500 transition-all duration-300 ease-in-out;
}

/* Enhanced admin accordion/collapse responsive improvements */
.admin-accordion {
  @apply border border-gray-200 rounded-lg overflow-hidden;
}

.admin-accordion-item {
  @apply border-b border-gray-200 last:border-b-0;
}

.admin-accordion-header {
  @apply w-full px-4 py-3 text-left bg-gray-50 hover:bg-gray-100;
  @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500;
  @apply transition-all duration-200;
  min-height: 48px;
}

@media (max-width: 640px) {
  .admin-accordion-header {
    @apply px-3 py-4;
    min-height: 52px;
  }
}

.admin-accordion-content {
  @apply px-4 py-3 bg-white;
}

@media (max-width: 640px) {
  .admin-accordion-content {
    @apply px-3 py-4;
  }
}

/* Calendar Page Enhancements */
.calendar-glassmorphism {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.calendar-gradient-bg {
  background: linear-gradient(135deg, #f8fafc 0%, #e1f5fe 50%, #f8fafc 100%);
}

.calendar-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.calendar-glow-primary {
  box-shadow: 0 0 20px rgba(10, 38, 71, 0.3);
}

.calendar-glow-secondary {
  box-shadow: 0 0 20px rgba(255, 84, 0, 0.3);
}

.calendar-glow-accent {
  box-shadow: 0 0 20px rgba(32, 82, 149, 0.3);
}

/* Enhanced Button Animations */
.calendar-button-bounce:hover {
  animation: bounce 0.6s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Gradient Text Animation */
.calendar-gradient-text {
  background: linear-gradient(45deg, #0A2647, #205295, #FF5400);
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating Animation */
.calendar-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Enhanced Focus States */
.calendar-focus-enhanced:focus {
  outline: none;
  ring: 3px solid rgba(10, 38, 71, 0.5);
  border-color: #0A2647;
  box-shadow: 0 0 0 3px rgba(10, 38, 71, 0.1);
}

/* Pulse Animation for Today Badge */
.calendar-pulse-today {
  animation: pulseToday 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulseToday {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Enhanced Search Bar */
.calendar-search-enhanced {
  position: relative;
  overflow: hidden;
}

.calendar-search-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.calendar-search-enhanced:focus::before {
  left: 100%;
}

/* Custom Scrollbar for Event Lists */
.calendar-events-scroll::-webkit-scrollbar {
  width: 6px;
}

.calendar-events-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.calendar-events-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #0A2647, #205295);
  border-radius: 10px;
}

.calendar-events-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #205295, #FF5400);
}

/* Event Card Border Animations */
.calendar-event-card {
  position: relative;
  overflow: hidden;
}

.calendar-event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(10, 38, 71, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.calendar-event-card:hover::before {
  transform: translateX(100%);
}

/* Loading Skeleton Enhancements */
.calendar-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Interactive Badge Animations */
.calendar-badge-interactive {
  transition: all 0.3s ease;
  cursor: pointer;
}

.calendar-badge-interactive:hover {
  transform: scale(1.1) rotate(2deg);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Typography Enhancements */
.calendar-title-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
  .calendar-card-hover:hover {
    transform: translateY(-4px);
  }
  
  .calendar-gradient-text {
    background-size: 200% 200%;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .calendar-glassmorphism {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .calendar-gradient-bg {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e293b 100%);
  }
}

/* Time Tracking Enhanced Styles */
.time-tracking-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.time-tracking-header {
  background: linear-gradient(135deg, #0A2647 0%, #205295 50%, #FF5400 100%);
  position: relative;
  overflow: hidden;
}

.time-tracking-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.time-tracking-timer-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.time-tracking-timer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(10, 38, 71, 0.02) 0%, rgba(32, 82, 149, 0.02) 50%, rgba(255, 84, 0, 0.02) 100%);
  border-radius: 24px;
}

.time-tracking-running-timer {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.1) 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  animation: pulse-green 2s ease-in-out infinite;
}

@keyframes pulse-green {
  0%, 100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
}

.time-tracking-stat-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.time-tracking-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.time-tracking-stat-card .stat-icon {
  transition: all 0.3s ease;
}

.time-tracking-stat-card:hover .stat-icon {
  transform: scale(1.1);
}

/* Progress bars for stats */
.stat-progress-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.stat-progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease;
  position: relative;
}

.stat-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced form inputs */
.time-tracking-input {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.time-tracking-input:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #0A2647;
  box-shadow: 0 0 0 3px rgba(10, 38, 71, 0.1);
  outline: none;
}

.time-tracking-select {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.time-tracking-select:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #0A2647;
  box-shadow: 0 0 0 3px rgba(10, 38, 71, 0.1);
  outline: none;
}

/* Enhanced buttons */
.time-tracking-btn-primary {
  background: linear-gradient(135deg, #0A2647 0%, #205295 100%);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 16px 32px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px -5px rgba(10, 38, 71, 0.3);
  position: relative;
  overflow: hidden;
}

.time-tracking-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px -5px rgba(10, 38, 71, 0.4);
}

.time-tracking-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.time-tracking-btn-primary:hover::before {
  left: 100%;
}

.time-tracking-btn-secondary {
  background: linear-gradient(135deg, #FF5400 0%, #ff6b1a 100%);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 8px 16px -4px rgba(255, 84, 0, 0.3);
}

.time-tracking-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px -4px rgba(255, 84, 0, 0.4);
}

.time-tracking-btn-accent {
  background: linear-gradient(135deg, #205295 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 8px 16px -4px rgba(32, 82, 149, 0.3);
}

.time-tracking-btn-accent:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px -4px rgba(32, 82, 149, 0.4);
}

/* Enhanced table styles */
.time-tracking-table {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.time-tracking-table-header {
  background: linear-gradient(135deg, rgba(10, 38, 71, 0.05) 0%, rgba(32, 82, 149, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.time-tracking-table-row {
  transition: all 0.2s ease;
}

.time-tracking-table-row:hover {
  background: rgba(10, 38, 71, 0.02);
}

/* Enhanced badges */
.time-tracking-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.time-tracking-badge-running {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  animation: pulse-badge 2s ease-in-out infinite;
}

@keyframes pulse-badge {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.time-tracking-badge-completed {
  background: linear-gradient(135deg, #0A2647 0%, #205295 100%);
  color: white;
}

.time-tracking-badge-billable {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
}

.time-tracking-badge-non-billable {
  background: linear-gradient(135deg, #FF5400 0%, #ff6b1a 100%);
  color: white;
}

/* Enhanced search and filter section */
.time-tracking-filters {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px;
  margin-bottom: 32px;
}

/* Notification styles */
.time-tracking-notification {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.time-tracking-notification-success {
  border-left: 4px solid #22c55e;
}

.time-tracking-notification-error {
  border-left: 4px solid #ef4444;
}

/* Enhanced dropdown menus */
.time-tracking-dropdown {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.time-tracking-dropdown-item {
  transition: all 0.2s ease;
}

.time-tracking-dropdown-item:hover {
  background: rgba(10, 38, 71, 0.05);
}

/* Mobile responsive enhancements */
@media (max-width: 768px) {
  .time-tracking-timer-card {
    border-radius: 16px;
    margin: 16px;
  }
  
  .time-tracking-stat-card {
    border-radius: 16px;
  }
  
  .time-tracking-table {
    border-radius: 16px;
  }
  
  .time-tracking-btn-primary {
    padding: 14px 28px;
    font-size: 14px;
  }
}

/* Loading states */
.time-tracking-loading {
  background: linear-gradient(90deg, rgba(10, 38, 71, 0.1) 25%, rgba(32, 82, 149, 0.1) 50%, rgba(10, 38, 71, 0.1) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Custom scrollbar */
.time-tracking-page ::-webkit-scrollbar {
  width: 8px;
}

.time-tracking-page ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.time-tracking-page ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #0A2647 0%, #205295 100%);
  border-radius: 4px;
}

.time-tracking-page ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #205295 0%, #FF5400 100%);
}

/* Admin Footer Styles */
.admin-layout-responsive footer {
  flex-shrink: 0;
  margin-top: auto;
  position: relative;
  z-index: 1;
}

/* Ensure footer stays at bottom on all admin pages */
.admin-layout-responsive .md\:pl-64 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Smooth image transitions for auto-slide */
.auto-slide-image {
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.auto-slide-image.fade-enter {
  opacity: 0;
  transform: scale(1.05);
}

.auto-slide-image.fade-enter-active {
  opacity: 1;
  transform: scale(1);
}

.auto-slide-image.fade-exit {
  opacity: 1;
  transform: scale(1);
}

.auto-slide-image.fade-exit-active {
  opacity: 0;
  transform: scale(0.95);
}
