import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { ImageItem } from '@/utils/getImages';
import { readPortfolioMetadata } from '@/app/api/admin/portfolio/route';
import ModernPageHero from '@/components/ModernPageHero';
import ModernLogoGallery from '@/components/ModernLogoGallery';
import LogoPackagesSectionDB from '@/components/LogoPackagesSectionDB';
import LogoTypesSection from '@/components/LogoTypesSection';
import ModernProcessSection from '@/components/ModernProcessSection';
import ModernFAQSection from '@/components/ModernFAQSection';
import LogoValuePropositionSection from '@/components/LogoValuePropositionSection';
import LogoColorsSection from '@/components/LogoColorsSection';
import CopyButton from '@/components/CopyButton';

// Enhanced caching configuration - revalidate every 5 minutes
export const revalidate = 300;
export const dynamic = 'force-static';

export const metadata: Metadata = {
  title: 'Logo Design Services | Mocky Digital Kenya',
  description: 'Professional logo design services in Kenya. Get a unique, memorable logo that represents your brand perfectly.',
  keywords: 'logo design kenya, professional logo design, brand identity, logo designer nairobi, corporate branding kenya',
};

// Function to fetch logos from the portfolio metadata directly with enhanced caching
async function getLogosFromPortfolio(): Promise<ImageItem[]> {
  try {
    // Use the portfolio metadata function directly to avoid HTTP fetch issues
    const portfolioItems = await readPortfolioMetadata();
    
    // Filter for logos only
    const logoItems = portfolioItems.filter(item => item.category === 'logos' && !item.deletedAt);
    
    // Convert to ImageItem format
    const logos: ImageItem[] = logoItems.map((item, index) => ({
      id: index + 1,
      url: item.imageSrc,
      src: item.imageSrc,
      alt: item.alt || item.title || 'Logo',
      title: item.title || '',
      category: 'logos',
      createdAt: item.createdAt,
      updatedAt: item.updatedAt
    }));
    
    console.log(`Loaded ${logos.length} logos from portfolio metadata`);
    return logos;
  } catch (error) {
    console.error('Error fetching logos from portfolio:', error);
    return [];
  }
}

// Logo types to display in the education section
const logoTypes = [
  {
    type: 'Wordmark',
    description: 'Text-based logos that focus on the company name',
    image: '/images/portfolio/logo-types/wordmark1.png',
    examples: ['Coca-Cola', 'Google', 'FedEx']
  },
  {
    type: 'Lettermark',
    description: 'Minimalist logos using initials or acronyms',
    image: '/images/portfolio/logo-types/lettermark1.png',
    examples: ['IBM', 'HBO', 'NASA']
  },
  {
    type: 'Symbol',
    description: 'Iconic, standalone graphics that represent the brand',
    image: '/images/portfolio/logo-types/symbol1.png',
    examples: ['Apple', 'Twitter', 'Nike']
  },
  {
    type: 'Combination',
    description: 'Text and symbol merged into one cohesive design',
    image: '/images/portfolio/logo-types/combination1.png',
    examples: ['Burger King', 'Amazon', 'Adidas']
  }
];

// Process steps for the logo design process
const processSteps = [
  {
    step: '01',
    title: 'Discovery',
    description: 'We begin by understanding your brand identity, target audience, and business goals. This crucial first step helps us create a logo that truly represents your brand and resonates with your customers.'
  },
  {
    step: '02',
    title: 'Concept',
    description: 'Based on your requirements, our designers create multiple unique concepts. Each concept is carefully crafted to reflect your brand personality and stand out in your industry.'
  },
  {
    step: '03',
    title: 'Revision',
    description: 'We refine the chosen concept based on your feedback, making adjustments to colors, typography, and other elements until the design perfectly matches your vision.'
  },
  {
    step: '04',
    title: 'Delivery',
    description: 'Once approved, we deliver your logo in all necessary file formats for both digital and print use, ensuring you have everything needed for consistent brand application.'
  }
];

// FAQs for the logo design service
const faqs = [
  {
    question: "How long does the logo design process take?",
    answer: "The timeline varies depending on the package you choose. Our Starter Package takes 48 hours, Standard Package takes 3 days, and Premium Package takes 5 days. Each package includes different numbers of revision rounds that may extend the timeline."
  },
  {
    question: "What file formats will I receive?",
    answer: "All packages include web-ready formats (JPG, PNG). Higher-tier packages include professional vector formats (AI, EPS, SVG) that are scalable to any size without losing quality, ideal for printing and professional applications."
  },
  {
    question: "Do I own the copyright to my logo?",
    answer: "Yes, once your project is complete and final payment is made, you own all rights to your logo design. We transfer full copyright ownership to you."
  },
  {
    question: "Can I request revisions to my logo design?",
    answer: "Yes, all packages include revision rounds. The Starter Package includes 3 rounds, the Standard Package includes 4 rounds, and the Premium Package includes unlimited revisions."
  },
  {
    question: "How do I get started with my logo design project?",
    answer: "Simply click the 'Order Now' button on your preferred package. You'll be asked to fill out a brief form with your logo requirements, including your business name, industry, logo type preferences, and any additional information. After submitting the form, you'll be connected with our design team via WhatsApp to discuss your project further."
  }
];

// Sample logos for the gallery - using real logo examples as fallback
const sampleLogos = [
  {
    id: 1,
    url: '/images/portfolio/logos/andytech.jpg',
    src: '/images/portfolio/logos/andytech.jpg',
    alt: 'AndyTech Logo'
  },
  {
    id: 2,
    url: '/images/portfolio/logos/marie-market.jpg',
    src: '/images/portfolio/logos/marie-market.jpg',
    alt: 'Marie Market Logo'
  },
  {
    id: 3,
    url: '/images/portfolio/logos/ascia.jpg',
    src: '/images/portfolio/logos/ascia.jpg',
    alt: 'Ascia Logo'
  },
  {
    id: 4,
    url: '/images/portfolio/logos/azhar.jpg',
    src: '/images/portfolio/logos/azhar.jpg',
    alt: 'Azhar Maize Flour Logo'
  },
  {
    id: 5,
    url: '/images/portfolio/logos/coppa.jpg',
    src: '/images/portfolio/logos/coppa.jpg',
    alt: 'Coppa Logo'
  },
  {
    id: 6,
    url: '/images/portfolio/logos/dawa.jpg',
    src: '/images/portfolio/logos/dawa.jpg',
    alt: 'Dawa Logo'
  },
  {
    id: 7,
    url: '/images/portfolio/logos/kofar.jpg',
    src: '/images/portfolio/logos/kofar.jpg',
    alt: 'Kofar Logo'
  },
  {
    id: 8,
    url: '/images/portfolio/logos/lachique.jpg',
    src: '/images/portfolio/logos/lachique.jpg',
    alt: 'LaChique Logo'
  }
];

export default async function LogosPage() {
  let logos: ImageItem[] = [];
  let error: Error | null = null;

  try {
    logos = await getLogosFromPortfolio();
    console.log(`Loaded ${logos.length} logos from portfolio metadata`);
  } catch (err) {
    error = err as Error;
    console.error('Error loading logos:', error);
  }

  return (
    <main>
      <ModernPageHero
        title="Professional Logo Design Services"
        description="Transform your brand with custom logo designs that capture your unique identity and drive business growth. From concept to completion, we create logos that make lasting impressions."
      />

      {/* Value Proposition Section */}
      <LogoValuePropositionSection />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">SHOWCASE</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Our Logo Designs</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Browse through our collection of professionally crafted logos that have helped businesses establish strong brand identities
            </p>
          </div>
          {error ? (
            <div className="text-red-600 text-center mb-8">
              <p>Error loading logos: {error.message}</p>
              <p className="text-sm mt-2">Showing sample logos instead.</p>
            </div>
          ) : null}
          
          {/* Show portfolio logos if available, otherwise show sample logos */}
          <ModernLogoGallery logos={logos.length > 0 ? logos : sampleLogos} />
          
          {logos.length === 0 && !error && (
            <div className="text-center text-gray-600 mt-8">
              <p>No portfolio logos available yet. Showing sample designs.</p>
            </div>
          )}
        </div>
      </section>

      {/* Logo Types Section */}
      <LogoTypesSection logoTypes={logoTypes} />

      {/* Pricing Section */}
      <section id="pricing-section">
        <LogoPackagesSectionDB />
      </section>

      {/* Onboarding Process Section */}
      <section className="py-16 sm:py-20 md:py-24 bg-gray-50" id="how-it-works">
        <div className="container mx-auto px-5 sm:px-6">
          <div className="text-center mb-12 sm:mb-16 md:mb-20">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">
              HOW IT WORKS
            </span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-5 text-gray-900">
              Our Logo Design Process
            </h2>
            <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto mt-4 sm:mt-5 px-2">
              From your initial brief to final delivery, we make logo design simple and stress-free
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 sm:gap-12 md:gap-16 lg:gap-24">
            {/* How It Works */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-10 shadow-sm hover:shadow-md transition-shadow duration-300">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-[#0A1929] flex items-center">
                <span className="inline-block w-7 h-7 sm:w-8 sm:h-8 bg-[#FF5400]/10 rounded-full mr-2 sm:mr-3 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#FF5400]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </span>
                Getting Started
              </h2>
              <p className="text-base sm:text-lg text-gray-600 mb-8 sm:mb-10 md:mb-12 border-l-4 border-[#FF5400]/30 pl-3 sm:pl-4 italic">
                Your journey to a perfect logo begins here
              </p>
              
              <div className="space-y-6 sm:space-y-8 md:space-y-10">
                <div className="flex gap-4 sm:gap-6 group">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-orange-100 flex items-center justify-center text-[#FF5400] font-bold text-xl group-hover:bg-[#FF5400] group-hover:text-white transition-colors duration-300">
                      1
                    </div>
                  </div>
                  <div className="pt-1 sm:pt-2">
                    <h3 className="text-lg sm:text-xl font-bold mb-1.5 sm:mb-2 group-hover:text-[#FF5400] transition-colors duration-300">Choose Your Package</h3>
                    <p className="text-gray-600 text-sm sm:text-base">Select the logo design package that best fits your needs and budget from our three options above</p>
                  </div>
                </div>
                
                <div className="flex gap-4 sm:gap-6 group">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-orange-100 flex items-center justify-center text-[#FF5400] font-bold text-xl group-hover:bg-[#FF5400] group-hover:text-white transition-colors duration-300">
                      2
                    </div>
                  </div>
                  <div className="pt-1 sm:pt-2">
                    <h3 className="text-lg sm:text-xl font-bold mb-1.5 sm:mb-2 group-hover:text-[#FF5400] transition-colors duration-300">Fill Design Brief</h3>
                    <p className="text-gray-600 text-sm sm:text-base">Complete our simple design brief form with your business details, preferences, and vision</p>
                  </div>
                </div>
                
                <div className="flex gap-4 sm:gap-6 group">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-orange-100 flex items-center justify-center text-[#FF5400] font-bold text-xl group-hover:bg-[#FF5400] group-hover:text-white transition-colors duration-300">
                      3
                    </div>
                  </div>
                  <div className="pt-1 sm:pt-2">
                    <h3 className="text-lg sm:text-xl font-bold mb-1.5 sm:mb-2 group-hover:text-[#FF5400] transition-colors duration-300">Make Deposit</h3>
                    <p className="text-gray-600 text-sm sm:text-base">Secure your project slot with a deposit payment via M-PESA to begin the design process</p>
                  </div>
                </div>
                
                <div className="flex gap-4 sm:gap-6 group">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-orange-100 flex items-center justify-center text-[#FF5400] font-bold text-xl group-hover:bg-[#FF5400] group-hover:text-white transition-colors duration-300">
                      4
                    </div>
                  </div>
                  <div className="pt-1 sm:pt-2">
                    <h3 className="text-lg sm:text-xl font-bold mb-1.5 sm:mb-2 group-hover:text-[#FF5400] transition-colors duration-300">Get Your Logo</h3>
                    <p className="text-gray-600 text-sm sm:text-base">Receive your custom logo designs, provide feedback, and get your final files</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Make Your Deposit */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-10 shadow-sm hover:shadow-md transition-shadow duration-300 mt-8 md:mt-0">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-[#0A1929] flex items-center">
                <span className="inline-block w-7 h-7 sm:w-8 sm:h-8 bg-[#FF5400]/10 rounded-full mr-2 sm:mr-3 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#FF5400]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                  </svg>
                </span>
                Payment Information
              </h2>
              <p className="text-base sm:text-lg text-gray-600 mb-8 sm:mb-10 md:mb-12 border-l-4 border-[#FF5400]/30 pl-3 sm:pl-4 italic">
                Quick and secure payment via M-PESA
              </p>
              
              <div className="bg-gray-50 rounded-lg sm:rounded-xl p-5 sm:p-6 md:p-8 shadow-sm mb-6 sm:mb-8 border border-gray-100 hover:border-orange-100 transition-colors duration-300">
                <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 sm:h-10 sm:w-10 text-[#FF5400]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg sm:text-xl font-bold">M-PESA Payment Details</h3>
                    <p className="text-sm sm:text-base text-gray-600">Use these details to make your deposit payment</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 gap-4 sm:gap-6 mb-6 sm:mb-8">
                  <div className="bg-white rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200 border border-gray-100">
                    <p className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2">Business Number (Pay Bill)</p>
                    <div className="flex items-center justify-between">
                      <p className="text-xl sm:text-2xl font-bold text-[#0A1929]">522533</p>
                      <CopyButton textToCopy="522533" label="Copy" />
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200 border border-gray-100">
                    <p className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2">Account Number</p>
                    <div className="flex items-center justify-between">
                      <p className="text-xl sm:text-2xl font-bold text-[#0A1929]">7934479</p>
                      <CopyButton textToCopy="7934479" label="Copy" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-orange-50 p-4 sm:p-5 rounded-lg border border-orange-100">
                  <div className="flex gap-3 sm:gap-4 items-start">
                    <div className="rounded-full bg-orange-100 p-1 sm:p-1.5 mt-1 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-[#FF5400]" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold mb-1 sm:mb-2 text-[#0A1929] text-sm sm:text-base">After Payment</h4>
                      <p className="text-xs sm:text-sm md:text-base text-gray-700">
                        Send your M-PESA confirmation message to us on WhatsApp 
                        <a href="https://wa.me/254741590670" className="text-[#FF5400] font-medium hover:underline ml-1">
                          (+254 741 590 670)
                        </a> 
                        along with your design brief to start your logo project immediately.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="text-center">
                <a
                  href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I%27m%20interested%20in%20your%20logo%20design%20services.%20Please%20provide%20me%20with%20the%20design%20brief%20form."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 bg-[#FF5400] hover:bg-[#e84a00] text-white px-6 py-3 rounded-lg font-semibold transition-colors shadow-md hover:shadow-lg"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.306"/>
                  </svg>
                  Get Design Brief Form
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Logo Colors Section */}
      <LogoColorsSection />

      {/* Process Section */}
      <ModernProcessSection steps={processSteps} />

      {/* FAQ Section */}
      <ModernFAQSection faqs={faqs} />
    </main>
  );
}