import Link from 'next/link';
import { BlogPost } from '@/types/blog';
import { Metadata } from 'next';
import BlogSearch from '@/components/BlogSearch';
import { Suspense } from 'react';
import Image from 'next/image';
import { ClockIcon, UserIcon, TagIcon, CalendarIcon } from '@heroicons/react/24/outline';

// Set the cache revalidation to 0 to prevent caching
export const revalidate = 0;

export const metadata: Metadata = {
  title: 'Blog | Mocky Digital',
  description: 'Read our latest articles on web design, graphic design, digital marketing, and more.',
};

// Loading component for blog posts
function BlogPostsLoading() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12 max-w-7xl mx-auto">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="flex flex-col border-t pt-6 animate-pulse">
          <div className="flex-1">
            <div className="flex items-center mb-3">
              <div className="h-3 bg-gray-200 rounded w-20"></div>
              <div className="mx-2 w-1 h-1 bg-gray-200 rounded-full"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </div>
            <div className="h-6 bg-gray-200 rounded mb-3"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Import the blog service directly for server-side data fetching
async function getBlogPosts(searchQuery?: string): Promise<BlogPost[]> {
  try {
    const { getAllBlogPosts, getPublishedBlogPosts } = await import('@/services/blogService');
    
    let posts = await getPublishedBlogPosts();
    
    // Apply search filter if provided
    if (searchQuery && searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      posts = posts.filter(post => 
        post.title.toLowerCase().includes(query) ||
        post.content.toLowerCase().includes(query) ||
        post.excerpt.toLowerCase().includes(query) ||
        post.category.toLowerCase().includes(query) ||
        post.author?.toLowerCase().includes(query) ||
        post.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    return posts;
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return [];
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

interface BlogPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  const resolvedSearchParams = await searchParams;
  const searchQuery = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined;
  
  const posts = await getBlogPosts(searchQuery);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-1 bg-[#FF5400]"></div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Our Blog
            </h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8">
              Insights, tips, and stories from the world of design and digital marketing
            </p>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-2xl mx-auto">
          <Suspense fallback={<div className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>}>
            <BlogSearch initialValue={searchQuery || ''} />
          </Suspense>
        </div>
      </div>

      {/* Blog Posts */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {posts.length === 0 ? (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                {searchQuery ? 'No posts found' : 'No blog posts yet'}
              </h3>
              <p className="mt-2 text-gray-500">
                {searchQuery 
                  ? `No posts match your search for "${searchQuery}". Try different keywords.`
                  : 'Check back soon for our latest insights and updates.'
                }
              </p>
            </div>
          </div>
        ) : (
          <>
            {searchQuery && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900">
                  Search Results for "{searchQuery}"
                </h2>
                <p className="text-gray-600 mt-2">
                  Found {posts.length} {posts.length === 1 ? 'post' : 'posts'}
                </p>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden group">
                  {/* Featured Image */}
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                    {post.featuredImage ? (
                      <Image
                        src={post.featuredImage}
                        alt={post.title}
                        width={400}
                        height={225}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gradient-to-br from-[#0A2647]/10 to-[#205295]/20 flex items-center justify-center">
                        <div className="text-center">
                          <svg className="mx-auto h-12 w-12 text-[#205295]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                          </svg>
                          <p className="text-[#0A2647] text-sm mt-2 font-medium">{post.category}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="p-6">
                    {/* Category Badge */}
                    <div className="mb-3">
                      <span className="inline-block px-3 py-1 text-xs font-semibold text-[#FF5400] bg-[#FF5400]/10 rounded-full">
                        {post.category}
                      </span>
                    </div>

                    {/* Title */}
                    <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#0A2647] transition-colors">
                      <Link href={`/blog/${post.slug}`} className="hover:underline">
                        {post.title}
                      </Link>
                    </h2>

                    {/* Excerpt */}
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {truncateText(post.excerpt, 120)}
                    </p>

                    {/* Tags */}
                    {post.tags && post.tags.length > 0 && (
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-2">
                          {post.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-[#205295] bg-[#205295]/10 rounded-md hover:bg-[#205295]/20 transition-colors"
                            >
                              <TagIcon className="h-3 w-3 mr-1" />
                              {tag}
                            </span>
                          ))}
                          {post.tags.length > 3 && (
                            <span className="text-xs text-gray-500 px-2 py-1">
                              +{post.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Meta Information */}
                    <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-4">
                        {/* Author */}
                        {post.author && (
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 mr-1" />
                            <span>{post.author}</span>
                          </div>
                        )}
                        
                        {/* Reading Time */}
                        {post.readingTime && (
                          <div className="flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            <span>{post.readingTime} min read</span>
                          </div>
                        )}
                      </div>

                      {/* Date */}
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        <time dateTime={post.publishedAt || post.createdAt}>
                          {formatDate(post.publishedAt || post.createdAt)}
                        </time>
                      </div>
                    </div>

                    {/* Read More Link */}
                    <div className="mt-4">
                      <Link
                        href={`/blog/${post.slug}`}
                        className="inline-flex items-center text-[#FF5400] hover:text-[#0A2647] font-medium transition-colors"
                      >
                        Read more
                        <svg className="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
