import nodemailer from 'nodemailer';

// Create a transporter using Gmail credentials
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'sncr fuit apix bitk'
  }
});

interface EmailOptions {
  subject: string;
  html: string;
}

export async function sendEmail(options: EmailOptions) {
  try {
    const mailOptions = {
      from: 'New Order <<EMAIL>>',
      to: '<EMAIL>',
      subject: options.subject,
      html: options.html,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }
}

export function generateOrderNotificationEmail(order: any) {
  const {
    orderNumber,
    customerName,
    email,
    phone,
    businessName,
    industry,
    logoType,
    slogan,
    additionalInfo,
    totalAmount,
    package: pkg,
  } = order;

  return {
    subject: `New Logo Order - ${orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Logo Order Received</h2>
        <p style="font-size: 16px; color: #374151;">A new logo design order has been received with the following details:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Order Details</h3>
          <p><strong>Order Number:</strong> ${orderNumber}</p>
          <p><strong>Package:</strong> ${pkg?.name || 'N/A'}</p>
          <p><strong>Amount:</strong> KSH${totalAmount}</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Customer Information</h3>
          <p><strong>Name:</strong> ${customerName}</p>
          <p><strong>Email:</strong> ${email}</p>
          ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
          <p><strong>Business Name:</strong> ${businessName}</p>
          ${industry ? `<p><strong>Industry:</strong> ${industry}</p>` : ''}
        </div>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Design Requirements</h3>
          ${logoType ? `<p><strong>Logo Type:</strong> ${logoType}</p>` : ''}
          ${slogan ? `<p><strong>Slogan:</strong> ${slogan}</p>` : ''}
          ${additionalInfo ? `<p><strong>Additional Info:</strong> ${additionalInfo}</p>` : ''}
        </div>
      </div>
    `
  };
}

export function generateCatalogueOrderNotificationEmail(order: any) {
  const {
    orderNumber,
    customerName,
    email,
    phone,
    productName,
    quantity,
    customQuantity,
    unitPrice,
    designFee,
    subtotal,
    totalAmount,
    paperType,
    printingSide,
    meters,
    needsDesign,
    designOnly,
    designBrief,
    artworkFiles,
    artworkFileCount,
    artworkUrls,
    deliveryMethod,
    deliveryAddress,
    notes
  } = order;

  // Format artwork links if they exist
  const artworkLinksHtml = artworkUrls && artworkUrls.length > 0 
    ? `
      <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #1f2937; margin-top: 0;">Artwork Files</h3>
        <ul style="margin: 0; padding-left: 20px;">
          ${artworkUrls.map((url: string, index: number) => `
            <li style="margin-bottom: 8px;">
              <a href="${url}" style="color: #2563eb; text-decoration: underline;" target="_blank">
                Artwork File ${index + 1}
              </a>
            </li>
          `).join('')}
        </ul>
      </div>
    `
    : '';

  return {
    subject: `New Catalogue Order - ${orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Catalogue Order Received</h2>
        <p style="font-size: 16px; color: #374151;">A new catalogue order has been received with the following details:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Order Details</h3>
          <p><strong>Order Number:</strong> ${orderNumber}</p>
          <p><strong>Product:</strong> ${productName}</p>
          <p><strong>Quantity:</strong> ${quantity}${customQuantity ? ' (Custom)' : ''}</p>
          ${meters ? `<p><strong>Meters:</strong> ${meters}</p>` : ''}
          ${paperType ? `<p><strong>Paper Type:</strong> ${paperType}</p>` : ''}
          ${printingSide ? `<p><strong>Printing Side:</strong> ${printingSide}</p>` : ''}
          <p><strong>Unit Price:</strong> KSH${unitPrice}</p>
          ${designFee > 0 ? `<p><strong>Design Fee:</strong> KSH${designFee}</p>` : ''}
          <p><strong>Subtotal:</strong> KSH${subtotal}</p>
          <p><strong>Total Amount:</strong> KSH${totalAmount}</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Customer Information</h3>
          <p><strong>Name:</strong> ${customerName}</p>
          <p><strong>Email:</strong> ${email}</p>
          ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
        </div>

        ${needsDesign || designOnly ? `
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">Design Requirements</h3>
            <p><strong>Service Type:</strong> ${designOnly ? 'Design Only' : 'Design & Print'}</p>
            ${designBrief ? `<p><strong>Design Brief:</strong> ${designBrief}</p>` : ''}
          </div>
        ` : ''}

        ${artworkLinksHtml}

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Delivery Information</h3>
          <p><strong>Method:</strong> ${deliveryMethod || 'Not specified'}</p>
          ${deliveryAddress ? `<p><strong>Address:</strong> ${deliveryAddress}</p>` : ''}
        </div>

        ${notes ? `
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">Additional Notes</h3>
            <p>${notes}</p>
          </div>
        ` : ''}
      </div>
    `
  };
} 