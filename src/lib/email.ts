import nodemailer from 'nodemailer';

// Create transporter (configure based on your email provider)
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

export async function sendPasswordResetEmail(
  email: string, 
  resetToken: string, 
  clientName: string
) {
  const resetUrl = `${process.env.NEXTAUTH_URL}?mode=reset&token=${resetToken}`;
  
  const mailOptions = {
    from: `"${process.env.COMPANY_NAME || 'Mocky Digital'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: 'Password Reset Request - Client Portal',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset Request</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${clientName},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            We received a request to reset your password for your client portal account. 
            If you didn't make this request, you can safely ignore this email.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 8px; 
                      font-weight: bold; 
                      display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px;">
            This link will expire in 1 hour for security reasons.
          </p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="color: #999; font-size: 12px;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${resetUrl}" style="color: #667eea;">${resetUrl}</a>
          </p>
          
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            Best regards,<br>
            ${process.env.COMPANY_NAME || 'Mocky Digital'} Team
          </p>
        </div>
      </div>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    return { success: false, error };
  }
}

export async function sendWelcomeEmail(
  email: string, 
  clientName: string, 
  verificationToken?: string
) {
  const verifyUrl = verificationToken 
          ? `${process.env.NEXTAUTH_URL}?mode=verify&token=${verificationToken}`
      : `${process.env.NEXTAUTH_URL}`;
  
  const mailOptions = {
    from: `"${process.env.COMPANY_NAME || 'Mocky Digital'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: 'Welcome to Our Client Portal!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to Our Client Portal!</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${clientName},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            Welcome to our client portal! Your account has been created successfully. 
            You now have access to track your projects, view documents, and collaborate with our team.
          </p>
          
          ${verificationToken ? `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verifyUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 8px; 
                        font-weight: bold; 
                        display: inline-block;">
                Verify Email & Access Portal
              </a>
            </div>
          ` : `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verifyUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 8px; 
                        font-weight: bold; 
                        display: inline-block;">
                Access Portal
              </a>
            </div>
          `}
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">What you can do in the portal:</h3>
            <ul style="color: #666; line-height: 1.6;">
              <li>📊 View real-time project progress and milestones</li>
              <li>📁 Access and download project documents</li>
              <li>💬 Submit feedback and communicate with our team</li>
              <li>📋 Approve project milestones and deliverables</li>
              <li>💳 View invoices and payment information</li>
              <li>📤 Upload files and documents securely</li>
            </ul>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
          </p>
          
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            Best regards,<br>
            ${process.env.COMPANY_NAME || 'Mocky Digital'} Team
          </p>
        </div>
      </div>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Failed to send welcome email:', error);
    return { success: false, error };
  }
}

export async function sendNotificationEmail(
  email: string, 
  clientName: string, 
  title: string, 
  message: string, 
  actionUrl?: string
) {
  const mailOptions = {
    from: `"${process.env.COMPANY_NAME || 'Mocky Digital'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: `${title} - Client Portal Notification`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 24px;">${title}</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${clientName},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            ${message}
          </p>
          
          ${actionUrl ? `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${actionUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 12px 25px; 
                        text-decoration: none; 
                        border-radius: 6px; 
                        font-weight: bold; 
                        display: inline-block;">
                View in Portal
              </a>
            </div>
          ` : ''}
          
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            Best regards,<br>
            ${process.env.COMPANY_NAME || 'Mocky Digital'} Team
          </p>
        </div>
      </div>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Failed to send notification email:', error);
    return { success: false, error };
  }
} 