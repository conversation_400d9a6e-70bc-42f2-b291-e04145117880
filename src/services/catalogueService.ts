import prisma from '@/lib/prisma';
import { z } from 'zod';
import { ErrorTracker } from '@/services/errorLogger';

// Validation schemas
export const CatalogueCreateSchema = z.object({
  service: z.string()
    .min(1, 'Service name is required')
    .max(255, 'Service name too long')
    .regex(/^[a-zA-Z0-9\s\-&.,()×:/"']+$/, 'Invalid characters in service name. Only letters, numbers, spaces, and common punctuation are allowed.'),
  price: z.number()
    .positive('Price must be positive')
    .max(10000000, 'Price too high')
    .int('Price must be a whole number'),
  designFee: z.number()
    .min(0, 'Design fee cannot be negative')
    .max(10000000, 'Design fee too high')
    .int('Design fee must be a whole number')
    .optional(),
  description: z.string()
    .max(1000, 'Description too long')
    .optional(),
  features: z.array(z.string().max(100, 'Feature too long'))
    .max(10, 'Too many features')
    .optional(),

  popular: z.boolean().optional(),
  imageUrl: z.union([
    z.string().url('Invalid image URL'),
    z.null()
  ]).optional(),
  imageUrl2: z.union([
    z.string().url('Invalid image URL'),
    z.null()
  ]).optional(),
  imageUrl3: z.union([
    z.string().url('Invalid image URL'),
    z.null()
  ]).optional(),
  category: z.string()
    .max(100, 'Category name too long')
    .regex(/^[a-zA-Z0-9\s\-&.,()×:/"']+$/, 'Invalid characters in category. Only letters, numbers, spaces, and common punctuation are allowed.')
    .optional()
});

export const CatalogueUpdateSchema = CatalogueCreateSchema.partial();

export interface Catalogue {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description?: string;
  features?: string[];
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CatalogueFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  popular?: boolean;
  search?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: 'service' | 'price' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// Get all catalogue items
export async function getAllCatalogue(): Promise<Catalogue[]> {
  try {
    console.log('Service: Fetching all catalogue items from database');
    const catalogueItems = await prisma.catalogue.findMany({
      orderBy: {
        service: 'asc'
      }
    });

    console.log(`Service: Found ${catalogueItems.length} catalogue items in database`);

    if (catalogueItems.length > 0) {
      console.log('Service: Sample catalogue item from DB:', JSON.stringify(catalogueItems[0]));
    }

    const formattedItems = catalogueItems.map(formatCatalogue);

    if (formattedItems.length > 0) {
      console.log('Service: Sample formatted catalogue item:', JSON.stringify(formattedItems[0]));
    }

    return formattedItems;
  } catch (error) {
    console.error('Service: Error getting all catalogue items:', error);
    throw new Error('Failed to fetch catalogue items from database');
  }
}

// Get a catalogue item by ID
export async function getCatalogueById(id: string): Promise<Catalogue | null> {
  try {
    // Validate ID format
    if (!id || typeof id !== 'string') {
      throw new Error('Invalid catalogue ID format');
    }

    const catalogueId = parseInt(id, 10);

    // Check if parsing was successful
    if (isNaN(catalogueId) || catalogueId <= 0) {
      throw new Error('Invalid catalogue ID: must be a positive integer');
    }

    const catalogue = await prisma.catalogue.findUnique({
      where: {
        id: catalogueId
      }
    });

    if (!catalogue) {
      return null;
    }

    return formatCatalogue(catalogue);
  } catch (error) {
    console.error(`Error getting catalogue by ID ${id}:`, error);
    throw error;
  }
}

// Create a new catalogue item
export async function createCatalogue(data: Omit<Catalogue, 'id' | 'createdAt' | 'updatedAt'>): Promise<Catalogue> {
  try {
    // Create catalogue data with all fields
    const newCatalogue = await prisma.catalogue.create({
      data: {
        service: data.service,
        price: data.price,
        designFee: data.designFee || 0,
        description: data.description || '',
        features: data.features || [],

        popular: data.popular || false,
        imageUrl: data.imageUrl,
        imageUrl2: data.imageUrl2,
        imageUrl3: data.imageUrl3,
        category: data.category || 'Other'
      }
    });

    // Return the formatted catalogue item
    return formatCatalogue(newCatalogue);
  } catch (error) {
    console.error('Error creating catalogue item:', error);
    throw error;
  }
}

// Update a catalogue item
export async function updateCatalogue(id: string, data: Partial<Catalogue>): Promise<Catalogue | null> {
  try {
    const catalogueId = parseInt(id);

    // Check if the catalogue item exists
    const existingCatalogue = await prisma.catalogue.findUnique({
      where: {
        id: catalogueId
      }
    });

    if (!existingCatalogue) {
      return null;
    }

    // Create an update data object with only the fields that are provided
    const updateData: any = {};
    if (data.service !== undefined) updateData.service = data.service;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.designFee !== undefined) updateData.designFee = data.designFee;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.features !== undefined) updateData.features = data.features;

    if (data.popular !== undefined) updateData.popular = data.popular;
    if (data.imageUrl !== undefined) updateData.imageUrl = data.imageUrl;
    if (data.imageUrl2 !== undefined) updateData.imageUrl2 = data.imageUrl2;
    if (data.imageUrl3 !== undefined) updateData.imageUrl3 = data.imageUrl3;
    if (data.category !== undefined) updateData.category = data.category;

    const updatedCatalogue = await prisma.catalogue.update({
      where: {
        id: catalogueId
      },
      data: updateData
    });

    // Return the formatted catalogue item
    return formatCatalogue(updatedCatalogue);
  } catch (error) {
    console.error(`Error updating catalogue ${id}:`, error);
    throw error;
  }
}

// Delete a catalogue item
export async function deleteCatalogue(id: string): Promise<boolean> {
  try {
    const catalogueId = parseInt(id);

    // Check if catalogue item has orders
    const ordersCount = await prisma.order.count({
      where: { productId: catalogueId }
    });

    if (ordersCount > 0) {
      console.error(`Cannot delete catalogue ${id}: has ${ordersCount} existing orders`);
      throw new Error(`Cannot delete catalogue item because it has ${ordersCount} existing order(s)`);
    }

    await prisma.catalogue.delete({
      where: {
        id: catalogueId
      }
    });

    return true;
  } catch (error) {
    console.error(`Error deleting catalogue ${id}:`, error);
    return false;
  }
}

// Bulk delete catalogue items
export async function bulkDeleteCatalogue(ids: string[]): Promise<{ success: boolean, count: number, errors?: string[] }> {
  try {
    // Convert string IDs to integers
    const catalogueIds = ids.map(id => parseInt(id));

    // Check which items have orders
    const itemsWithOrders = await prisma.order.findMany({
      where: { productId: { in: catalogueIds } },
      select: {
        productId: true,
        product: { select: { service: true } }
      },
      distinct: ['productId']
    });

    const idsWithOrders = itemsWithOrders.map(order => order.productId);
    const itemsToDelete = catalogueIds.filter(id => !idsWithOrders.includes(id));

    const errors: string[] = [];
    if (idsWithOrders.length > 0) {
      const skippedItems = itemsWithOrders.map(item => ({
        id: item.productId,
        name: item.product?.service || 'Unknown'
      }));
      errors.push(`Cannot delete items with existing orders: ${skippedItems.map(item => `${item.name} (ID: ${item.id})`).join(', ')}`);
    }

    let deleteCount = 0;
    if (itemsToDelete.length > 0) {
      const result = await prisma.catalogue.deleteMany({
        where: {
          id: { in: itemsToDelete }
        }
      });
      deleteCount = result.count;
    }

    return {
      success: deleteCount > 0 || errors.length === 0,
      count: deleteCount,
      errors: errors.length > 0 ? errors : undefined
    };
  } catch (error) {
    console.error(`Error bulk deleting catalogue items:`, error);
    return {
      success: false,
      count: 0,
      errors: ['Failed to perform bulk delete operation']
    };
  }
}

// Helper function to format catalogue data from the database
function formatCatalogue(catalogue: any): Catalogue {
  // The database might not have the new fields yet, so we'll handle that case
  return {
    id: catalogue.id.toString(),
    service: catalogue.service,
    price: catalogue.price,
    designFee: catalogue.designFee || 0,
    description: catalogue.description || '',
    // These fields might not exist in the database yet
    features: catalogue.features || [],

    popular: catalogue.popular || false,
    imageUrl: catalogue.imageUrl || undefined,
    imageUrl2: catalogue.imageUrl2 || undefined,
    imageUrl3: catalogue.imageUrl3 || undefined,
    category: catalogue.category || 'Other',
    createdAt: catalogue.createdAt.toISOString(),
    updatedAt: catalogue.updatedAt.toISOString()
  };
}
